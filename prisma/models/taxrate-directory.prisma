/// 税率字典 - 版本
model TaxrateDictionaryVersion {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  name   String       @map("name") /// 名称
  status EnableStatus @default(NOT_ENABLED) @map("status") /// 启用状态

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  taxrateDictionaryCategory TaxrateDictionaryCategory[] /// 分类表
  taxratelDictionaryDetail  TaxrateDictionaryDetail[] /// 明细表

  @@unique([tenantId, orgId, id])
  @@map("taxratel_dictionary_version")
}

/// 税率字典 - 分类
model TaxrateDictionaryCategory {
  // 主键 & 外键
  tenantId                   String @map("tenant_id") /// 租户id
  orgId                      String @map("org_id") /// 组织id
  id                         String @id @default(uuid(7)) @map("id") /// 数据id
  taxrateDictionaryVersionId String @map("taxrate_dictionary_version_id") /// 税率字典版本id

  // 业务字段
  code     String  @map("code") /// 编码
  name     String  @map("name") /// 名称
  remark   String? @map("remark") /// 备注
  isActive Boolean @default(true) @map("is_active") /// 是否启用

  // 树形结构字段
  parentId String? @map("parent_id") /// 父级id
  fullId   String  @default("") @map("full_id") /// 全路径id(使用/分隔)
  fullName String  @default("") @map("full_name") /// 全名称(使用/分隔)
  level    Int     @default(1) @map("level") /// 级别
  sort     Int     @default(1) @map("sort") /// 排序
  isLeaf   Boolean @default(true) @map("is_leaf") /// 是否叶子节点

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  children TaxrateDictionaryCategory[] @relation("taxrateDictionaryCategoryHierarchy")
  parent   TaxrateDictionaryCategory?  @relation("taxrateDictionaryCategoryHierarchy", fields: [parentId], references: [id])

  // 模型关联
  taxrateDictionaryVersion TaxrateDictionaryVersion  @relation(fields: [taxrateDictionaryVersionId], references: [id]) /// 税率字典 - 版本
  taxrateDictionaryDetail  TaxrateDictionaryDetail[] /// 税率字典 - 明细

  @@unique([tenantId, orgId, id])
  @@map("taxrate_dictionary_category")
}

/// 税率字典 - 明细
model TaxrateDictionaryDetail {
  // 主键 & 外键
  tenantId                    String @map("tenant_id") /// 租户id
  orgId                       String @map("org_id") /// 组织id
  id                          String @id @default(uuid(7)) @map("id") /// 数据id
  taxrateDictionaryVersionId  String @map("taxrate_dictionary_version_id") /// 税率字典版本id
  taxrateDictionaryCategoryId String @map("taxrate_dictionary_category_id") /// 税率字典分类id

  // 业务字段
  code       String      @map("code") /// 编码
  name       String      @map("name") /// 名称
  type       TaxrateType @map("type") /// 发票类型
  taxRate    String      @map("tax_rate") /// 税率
  remark     String?     @map("remark") /// 备注
  sort       Int         @default(1) @map("sort") /// 排序
  isActive   Boolean     @default(true) @map("is_active") /// 是否启用
  excuteDate DateTime?   @map("excute_date") /// 执行时间

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  taxrateDictionaryVersion      TaxrateDictionaryVersion        @relation(fields: [taxrateDictionaryVersionId], references: [id])
  taxrateDictionaryCategory     TaxrateDictionaryCategory       @relation(fields: [taxrateDictionaryCategoryId], references: [id])
  TaxrateDictionaryChangeRecord TaxrateDictionaryChangeRecord[] /// 税率字典-变更记录

  @@unique([tenantId, orgId, id])
  @@map("taxrate_dictionary_detail")
}

/// 税率字典 - 已启用的明细
model TaxrateDictionaryEnableDetail {
  // 主键 & 外键
  tenantId                    String @map("tenant_id") /// 租户id
  orgId                       String @map("org_id") /// 组织id
  id                          String @id @default(uuid(7)) @map("id") /// 数据id
  taxrateDictionaryVersionId  String @map("taxrate_dictionary_version_id") /// 税率字典版本id
  taxrateDictionaryCategoryId String @map("taxrate_dictionary_category_id") /// 税率字典分类id

  // 业务字段
  taxRate String @map("tax_rate") /// 税率

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  @@unique([tenantId, orgId, id])
  @@map("taxrate_dictionary_enable_detail")
}

/// 税率字典 - 变更记录
model TaxrateDictionaryChangeRecord {
  // 主键 & 外键
  tenantId                  String @map("tenant_id") /// 租户id
  orgId                     String @map("org_id") /// 组织id
  id                        String @id @default(uuid(7)) @map("id") /// 数据id
  taxrateDictionaryDetailId String @map("taxrate_dictionary_detail_id") /// 税率字典明细id

  // 业务字段
  fieldName       String @map("field_name") /// 字段名称
  oldValue        String @map("old_value") /// 旧值
  newValue        String @map("new_value") /// 新值
  opreateUserId   String @map("opreate_user_id") /// 操作人id
  opreateUserName String @map("opreate_user_name") /// 操作人名称

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  taxrateDictionaryDetail TaxrateDictionaryDetail @relation(fields: [taxrateDictionaryDetailId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("taxrate_dictionary_change_record")
}
