/// 字典类型
enum Dict_Type {
  BUSINESS_COST_SUBJECT // 业务成本科目发布
  SUBCONTRACT_STANDARD_BILL // 分包标准清单
  MATERIAL_DICTIONARY // 材料字典
  MECHANICAL_DICTIONARY // 机械字典
  COST_DICTIONARY // 费用字典
  TAXRATE_DICTIONARY // 税率字典
}

/// 字典名称
model AccountDictionary {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  name   String    @map("name") /// 名称
  status Boolean   @default(false) @map("status") /// 状态
  type   Dict_Type @map("type") /// 字典类型
  sort   Int       @map("sort") /// 排序

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  accountBusinessCostSubjectVersion AccountBusinessCostSubjectVersion[] /// 引用业务成本科目字典版本

  accountMaterialDictionaryVersion  AccountMaterialDictionaryVersion[] /// 引用材料字典版本
  accountMaterialDictionaryCategory AccountMaterialDictionaryCategory[] /// 引用材料字典类别
  accountMaterialDictionaryDetail   AccountMaterialDictionaryDetail[] /// 引用材料字典明细

  accountMechineryDictionaryVersion  AccountMechineryDictionaryVersion[] /// 引用机械字典版本
  accountMechineryDictionaryCategory AccountMechineryDictionaryCategory[] /// 引用机械字典类别
  accountMechineryDictionaryDetail   AccountMechineryDictionaryDetail[] /// 引用机械字典明细

  AccountCostDictionaryVersion  AccountCostDictionaryVersion[] /// 引用费用字典版本
  AccountCostDictionaryCategory AccountCostDictionaryCategory[] /// 引用费用字典类别
  AccountCostDictionaryDetail   AccountCostDictionaryDetail[] /// 引用费用字典明细

  AccountTaxRateDictionaryVersion  AccountTaxRateDictionaryVersion[] /// 引用税率字典版本
  AccountTaxRateDictionaryCategory AccountTaxRateDictionaryCategory[] ///引用费用字典类别
  AccountTaxRateDictionaryDetail   AccountTaxRateDictionaryDetail[] ///引用费用字典明细

  @@unique([tenantId, orgId, id])
  @@map("account_dictionary")
}
