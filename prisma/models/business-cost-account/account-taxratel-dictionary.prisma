/// 业务成本核算引用税率字典版本
model AccountTaxRateDictionaryVersion {
  // 主键 & 外键
  tenantId            String @map("tenant_id") /// 租户id
  orgId               String @map("org_id") /// 组织id
  id                  String @id @default(uuid(7)) @map("id") /// 数据id
  accountDictionaryId String @map("account_dictionary_id") /// 字典名称id

  // 业务字段
  versionId String @map("version_id") /// 版本id

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  accountDictionary AccountDictionary @relation(fields: [accountDictionaryId], references: [id]) /// 账户字典
  // materialDictionaryVersion         MaterialDictionaryVersion           @relation(fields: [versionId], references: [id])
  // accountMaterialDictionaryCategory AccountMaterialDictionaryCategory[] ///  账户字典分类
  // accountMaterialDictionaryDetail   AccountMaterialDictionaryDetail[] /// 账户字典详情

  @@unique([tenantId, orgId, id])
  @@map("account_taxratel_dictionary_version")
}

/// 业务成本核算引用税率字典分类
model AccountTaxRateDictionaryCategory {
  // 主键 & 外键
  tenantId            String @map("tenant_id") /// 租户id
  orgId               String @map("org_id") /// 组织id
  id                  String @id @default(uuid(7)) @map("id") /// 数据id
  accountDictionaryId String @map("account_dictionary_id") /// 字典名称id
  // accountMaterialDictionaryVersionId String @map("account_material_dictionary_version_id") /// 绑定的字典版本id

  // 业务字段
  categoryId String @map("category_id") /// 分类id
  versionId  String @map("version_id") /// 版本id

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  accountDictionary AccountDictionary @relation(fields: [accountDictionaryId], references: [id])
  // materialDictionaryCategory         MaterialDictionaryCategory        @relation(fields: [categoryId], references: [id])
  // accountMaterialDictionaryVersion AccountMaterialDictionaryVersion  @relation(fields: [accountMaterialDictionaryVersionId], references: [id])
  // accountMaterialDictionaryDetail  AccountMaterialDictionaryDetail[] /// 账户字典详情

  @@unique([tenantId, orgId, id])
  @@map("account_taxratel_dictionary_category")
}

/// 业务成本核算引用税率字典分类
model AccountTaxRateDictionaryDetail {
  // 主键 & 外键
  tenantId            String @map("tenant_id") /// 租户id
  orgId               String @map("org_id") /// 组织id
  id                  String @id @default(uuid(7)) @map("id") /// 数据id
  accountDictionaryId String @map("account_dictionary_id") /// 字典名称id

  // 业务字段
  categoryId String @map("category_id") /// 分类id
  versionId  String @map("version_id") /// 版本id
  detailId   String @map("detail_id") /// 详情id

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  accountDictionary AccountDictionary @relation(fields: [accountDictionaryId], references: [id])
  // AccountMaterialDictionaryCategory AccountMaterialDictionaryCategory @relation(fields: [categoryId], references: [id])
  // AccountMaterialDictionaryVersion  AccountMaterialDictionaryVersion  @relation(fields: [versionId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("account_taxratel_dictionary_detail")
}
