/// 业务成本科目-版本
model BusinessCostSubjectVersion {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  name   String       @map("name") /// 名称
  status EnableStatus @default(NOT_ENABLED) @map("status") /// 启用状态

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  businessCostSubjectCategory BusinessCostSubjectCategory[]
  businessCostSubjectDetail   BusinessCostSubjectDetail[]

  @@unique([tenantId, orgId, id])
  @@map("business_cost_subject_version")
}

/// 业务成本科目-分类
model BusinessCostSubjectCategory {
  // 主键 & 外键
  tenantId                     String @map("tenant_id") /// 租户id
  orgId                        String @map("org_id") /// 组织id
  id                           String @id @default(uuid(7)) @map("id") /// 数据id
  businessCostSubjectVersionId String @map("business_cost_subject_version_id") /// 业务成本科目版本id

  // 业务字段
  code     String  @map("code") /// 编码
  name     String  @map("name") /// 名称
  remark   String? @map("remark") /// 备注
  isActive Boolean @default(true) @map("is_active") /// 是否启用

  // 树形结构字段
  parentId String? @map("parent_id") /// 父级id
  fullId   String  @default("") @map("full_id") /// 全路径id(使用/分隔)
  fullName String  @default("") @map("full_name") /// 全名称(使用/分隔)
  level    Int     @default(1) @map("level") /// 级别
  sort     Int     @default(1) @map("sort") /// 排序
  isLeaf   Boolean @default(true) @map("is_leaf") /// 是否叶子节点

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  parent                     BusinessCostSubjectCategory?  @relation("BusinessCostSubjectCategoryTree", fields: [parentId], references: [id])
  children                   BusinessCostSubjectCategory[] @relation("BusinessCostSubjectCategoryTree")
  businessCostSubjectVersion BusinessCostSubjectVersion    @relation(fields: [businessCostSubjectVersionId], references: [id])
  businessCostSubjectDetail  BusinessCostSubjectDetail[]

  @@unique([tenantId, orgId, id])
  @@map("business_cost_subject_category")
}

/// 业务成本科目-明细
model BusinessCostSubjectDetail {
  // 主键 & 外键
  tenantId                      String  @map("tenant_id") /// 租户id
  orgId                         String  @map("org_id") /// 组织id
  id                            String  @id @default(uuid(7)) @map("id") /// 数据id
  businessCostSubjectVersionId  String  @map("business_cost_subject_version_id") /// 业务成本科目版本id
  businessCostSubjectCategoryId String  @map("business_cost_subject_category_id") // 业务成本科目分类id
  financialCostSubjectId        String? @map("financial_cost_subject_id") // 财务成本科目id

  // 业务字段
  code                      String   @map("code") /// 编码
  name                      String   @map("name") /// 名称
  unit                      String?  @map("unit") /// 单位
  expenseCategory           String?  @map("expense_category") /// 费用类别
  accountingDescription     String?  @map("accounting_description") /// 核算说明
  isSafetyConstructionFee   Boolean? @default(false) @map("is_safety_construction_fee") /// 安全施工费
  subjectMappingDescription String?  @map("subject_mapping_description") /// 科目对照说明
  financialCostSubjectName  String?  @map("financial_cost_subject_name") /// 财务成本科目名称(冗余字段)
  sort                      Int      @default(1) @map("sort") /// 排序
  isActive                  Boolean  @default(true) @map("is_active") /// 是否启用

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  businessCostSubjectVersion               BusinessCostSubjectVersion                 @relation(fields: [businessCostSubjectVersionId], references: [id])
  businessCostSubjectCategory              BusinessCostSubjectCategory                @relation(fields: [businessCostSubjectCategoryId], references: [id])
  financialCostSubject                     FinancialCostSubject?                      @relation(fields: [financialCostSubjectId], references: [id])
  subcontractBillDetailBusinessCostDetail  SubcontractBillDetailBusinessCostDetail[] /// 分包标准清单明细 & 业务成本科目明细 - 中间表
  materialDetailBusinessCostSubjectDetail  MaterialDetailBusinessCostSubjectDetail[] /// 材料字典明细 & 业务成本科目明细 - 中间表
  machineryDetailBusinessCostSubjectDetail MachineryDetailBusinessCostSubjectDetail[] /// 机械字典明细 & 业务成本科目明细 - 中间表
  costDetailBusinessCostSubjectDetail      CostDetailBusinessCostSubjectDetail[] /// 费用字典明细 & 业务成本科目明细 - 中间表

  @@unique([tenantId, orgId, id])
  @@map("business_cost_subject_detail")
}
