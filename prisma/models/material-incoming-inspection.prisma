/// 材料进场验收单-单据表
model MaterialIncomingInspection {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  materialReceiptStatus MaterialReceiptStatus @default(UN_RECEIVED) @map("material_receipt_status") /// 收料状态
  code                  String                @map("code") /// 单据编码
  purchaseType          PurchaseType          @map("purchase_type") /// 采购类型
  supplierId            String?               @map("supplier_id") /// 供应商ID
  supplierName          String?               @map("supplier_name") /// 供应商名称
  contractId            String?               @map("contract_id") /// 合同ID
  contractName          String?               @map("contract_name") /// 合同名称
  submitStatus          SubmitStatus          @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus           AuditStatus           @default(PENDING) @map("audit_status") /// 审批状态
  year                  Int                   @map("year") /// 年
  month                 Int                   @map("month") /// 月
  day                   Int                   @map("day") /// 日
  creator               String                @map("creator") /// 创建人名称

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_incoming_inspection")
}

// 材料进场验收单-材料明细表
model MaterialIncomingInspectionDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  incomingInspectionId  String   @map("incoming_inspection_id") /// 验收单ID
  materialId            String   @map("material_id") /// 材料ID
  materialName          String?  @map("material_name") /// 材料名称
  materialSpec          String?  @map("material_spec") /// 材料规格
  materialCategoryName  String?  @map("material_category_name") // 材料类别名称(二级/一级类别名称)
  qualityStandard       String?  @map("quality_standard") /// 质量标准
  unit                  String   @map("unit") /// 计量单位
  siteEntryQuantity     Decimal? @map("site_entry_quantity") @db.Decimal(20, 8) /// 进场数量
  actualQuantity        Decimal? @map("actual_quantity") @db.Decimal(20, 8) /// 实收数量
  appearanceDescription String?  @map("appearance_description") /// 外观质量描述
  orderNo               BigInt   @default(autoincrement()) @map("order_no") /// 排序号
  remark                String?  @map("remark") /// 备注

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_incoming_inspection_detail")
}

// 材料进场验收单-附件表
model MaterialIncomingInspectionAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  incomingInspectionId String @map("incoming_inspection_id") /// 验收单ID
  fileName             String @map("file_name") /// 文件名称
  fileKey              String @map("file_key") /// 文件key
  fileSize             Int    @map("file_size") /// 文件大小
  fileExt              String @map("file_ext") /// 文件后缀
  fileContentType      String @map("file_content_type") /// 文件contentType

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_incoming_inspection_attachment")
}
