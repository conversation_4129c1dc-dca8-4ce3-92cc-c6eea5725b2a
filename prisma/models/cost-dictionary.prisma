/// 费用字典 - 版本
model CostDictionaryVersion {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  name                         String       @map("name") /// 名称
  businessCostSubjectVersionId String       @map("business_cost_subject_version_id") /// 业务成本成本科目版本id
  status                       EnableStatus @default(NOT_ENABLED) @map("status") /// 启用状态

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  costDictionaryCategory CostDictionaryCategory[] /// 分类表
  costDictionaryDetail   CostDictionaryDetail[] /// 明细表

  @@unique([tenantId, orgId, id])
  @@map("cost_dictionary_version")
}

/// 费用字典 - 分类
model CostDictionaryCategory {
  // 主键 & 外键
  tenantId                String @map("tenant_id") /// 租户id
  orgId                   String @map("org_id") /// 组织id
  id                      String @id @default(uuid(7)) @map("id") /// 数据id
  costDictionaryVersionId String @map("cost_dictionary_version_id") /// 机械字典版本id

  // 业务字段
  code     String  @map("code") /// 编码
  name     String  @map("name") /// 名称
  remark   String? @map("remark") /// 备注
  isActive Boolean @default(true) @map("is_active") /// 是否启用

  // 树形结构字段
  parentId String? @map("parent_id") /// 父级id
  fullId   String  @default("") @map("full_id") /// 全路径id(使用/分隔)
  fullName String  @default("") @map("full_name") /// 全名称(使用/分隔)
  level    Int     @default(1) @map("level") /// 级别
  sort     Int     @default(1) @map("sort") /// 排序
  isLeaf   Boolean @default(true) @map("is_leaf") /// 是否叶子节点

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  children CostDictionaryCategory[] @relation("costDictionaryCategoryHierarchy")
  parent   CostDictionaryCategory?  @relation("costDictionaryCategoryHierarchy", fields: [parentId], references: [id])

  // 模型关联
  costDictionaryVersion CostDictionaryVersion  @relation(fields: [costDictionaryVersionId], references: [id]) /// 机械字典 - 版本
  costDictionaryDetail  CostDictionaryDetail[] /// 费用字典 - 明细

  @@unique([tenantId, orgId, id])
  @@map("cost_dictionary_category")
}

/// 费用字典 - 明细
model CostDictionaryDetail {
  // 主键 & 外键
  tenantId                 String @map("tenant_id") /// 租户id
  orgId                    String @map("org_id") /// 组织id
  id                       String @id @default(uuid(7)) @map("id") /// 数据id
  costDictionaryVersionId  String @map("cost_dictionary_version_id") /// 机械字典版本id
  costDictionaryCategoryId String @map("cost_dictionary_category_id") /// 机械字典分类id

  // 业务字段
  code               String  @map("code") /// 编码
  name               String  @map("name") /// 名称
  remark             String? @map("remark") /// 备注
  accountExplanation String? @map("account_explanation") /// 核算说明
  sort               Int     @default(1) @map("sort") /// 排序
  isActive           Boolean @default(true) @map("is_active") /// 是否启用

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  costDictionaryVersion               CostDictionaryVersion                 @relation(fields: [costDictionaryVersionId], references: [id])
  costDictionaryCategory              CostDictionaryCategory                @relation(fields: [costDictionaryCategoryId], references: [id])
  costDetailBusinessCostSubjectDetail CostDetailBusinessCostSubjectDetail[] /// 机械字典明细 & 业务成本科目明细 - 中间表

  @@unique([tenantId, orgId, id])
  @@map("cost_dictionary_detail")
}

/// 费用字典明细 & 业务成本科目明细 - 中间表
model CostDetailBusinessCostSubjectDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  businessCostSubjectDetailId String @map("business_cost_subject_detail_id") /// 业务成本科目明细 id
  costDictionaryDetailId      String @map("cost_dictionary_detail_id") /// 机械字典明细 id

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  costDictionaryDetail      CostDictionaryDetail      @relation(fields: [costDictionaryDetailId], references: [id])
  businessCostSubjectDetail BusinessCostSubjectDetail @relation(fields: [businessCostSubjectDetailId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("cost_detail_business_cost_subject_detail")
}
