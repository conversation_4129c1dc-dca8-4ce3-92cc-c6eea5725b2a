/// 分包标准清单-版本
model SubcontractStandardBillVersion {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  name                         String       @map("name") /// 名称
  businessCostSubjectVersionId String       @map("business_cost_subject_version_id") /// 业务成本成本科目版本id
  status                       EnableStatus @default(NOT_ENABLED) @map("status") /// 启用状态

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  subcontractStandardBillCategory     SubcontractStandardBillCategory[] /// 分类表
  subcontractingStandardSubjectDetail SubcontractStandardBillDetail[] /// 明细表

  @@unique([tenantId, orgId, id])
  @@map("subcontract_standard_bill_version")
}

/// 分包标准清单-分类
model SubcontractStandardBillCategory {
  // 主键 & 外键
  tenantId                         String @map("tenant_id") /// 租户id
  orgId                            String @map("org_id") /// 组织id
  id                               String @id @default(uuid(7)) @map("id") /// 数据id
  subcontractStandardBillVersionId String @map("subcontract_standard_bill_version_id") /// 分包标准清单版本id

  // 业务字段
  code     String  @map("code") /// 编码
  name     String  @map("name") /// 名称
  remark   String? @map("remark") /// 备注
  isActive Boolean @default(true) @map("is_active") /// 是否启用

  // 树形结构字段
  parentId String? @map("parent_id") /// 父级id
  fullId   String  @default("") @map("full_id") /// 全路径id(使用/分隔)
  fullName String  @default("") @map("full_name") /// 全名称(使用/分隔)
  level    Int     @default(1) @map("level") /// 级别
  sort     Int     @default(1) @map("sort") /// 排序
  isLeaf   Boolean @default(true) @map("is_leaf") /// 是否叶子节点

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  subcontractingStandardSubjectVersion SubcontractStandardBillVersion  @relation(fields: [subcontractStandardBillVersionId], references: [id])
  subcontractingStandardSubjectDetail  SubcontractStandardBillDetail[]

  @@unique([tenantId, orgId, id])
  @@map("subcontract_standard_bill_category")
}

/// 分包标准清单-明细表
model SubcontractStandardBillDetail {
  // 主键 & 外键
  tenantId                          String @map("tenant_id") /// 租户id
  orgId                             String @map("org_id") /// 组织id
  id                                String @id @default(uuid(7)) @map("id") /// 数据id
  subcontractStandardBillVersionId  String @map("subcontract_standard_bill_version_id") /// 分包标准清单发布版本 id
  subcontractStandardBillCategoryId String @map("subcontracting_standard_subject_category_id") /// 分包标准清单发布分类 id

  // 业务字段
  code                  String   @map("code") /// 编码
  name                  String   @map("name") /// 项目名称
  workContent           String   @map("work_content") /// 工作内容
  contractingMaterials  String   @map("contracting_materials") /// 乙方承包材料
  calculationRules      String   @map("calculation_rules") /// 计算规则
  unit                  String   @map("unit") /// 计量单位
  accountingExplanation String   @map("accounting_explanation") /// 核算说明
  // 公共字段
  isDeleted             Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy              String   @default("system") @map("create_by") /// 创建人
  updateBy              String   @default("system") @map("update_by") /// 更新人
  createAt              DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt              DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  subcontractStandardBillVersion          SubcontractStandardBillVersion            @relation(fields: [subcontractStandardBillVersionId], references: [id])
  subcontractStandardBillCategory         SubcontractStandardBillCategory           @relation(fields: [subcontractStandardBillCategoryId], references: [id])
  subcontractBillDetailBusinessCostDetail SubcontractBillDetailBusinessCostDetail[]

  @@unique([tenantId, orgId, id])
  @@map("subcontract_standard_bill_detail")
}

/// 分包标准清单发布明细 & 业务成本科目明细 - 中间表
model SubcontractBillDetailBusinessCostDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  businessCostSubjectDetailId     String @map("business_cost_subject_detail_id") /// 业务成本科目发布明细 id
  subcontractStandardBillDetailId String @map("subcontracting_standard_subject_detail_id") /// 分包标准清单发布明细id

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 模型关联
  subcontractStandardBillDetail SubcontractStandardBillDetail @relation(fields: [subcontractStandardBillDetailId], references: [id])
  businessCostSubjectDetail     BusinessCostSubjectDetail     @relation(fields: [businessCostSubjectDetailId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("subcontract_bill_detail_business_cost_detail")
}
