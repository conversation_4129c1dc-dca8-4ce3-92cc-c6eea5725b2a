import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import * as Joi from 'joi';

import appConfig from './app.config';
import databaseConfig from './database.config';

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true, // 全局可用
      cache: true, // 启用缓存
      load: [appConfig, databaseConfig], // 加载配置
      envFilePath: ['.env', '.env.local'], // 环境变量文件路径
      validationSchema: Joi.object({
        // 数据库配置验证
        DATABASE_URL: Joi.string().required(),
        LOG_QUERY_EVENT: Joi.boolean().default(false),

        // 应用配置验证
        APP_PORT: Joi.number().default(3000),
        JWT_SECRET: Joi.string().required(),
        JWT_EXPIRES_IN: Joi.string().default('2h'),
        JWT_REFRESH_EXPIRES_IN: Joi.string().default('5d'),

        // OBS配置验证
        OBS_ENDPOINT: Joi.string().required(),
        OBS_DOMAIN_NAME: Joi.string().required(),
        OBS_USERNAME: Joi.string().required(),
        OBS_AK: Joi.string().required(),
        OBS_SK: Joi.string().required(),
        OBS_BUCKET: Joi.string().required(),

        // 调用其他服务的api地址
        PLATFORM_API_URL: Joi.string().required()
      }),
      validationOptions: {
        allowUnknown: true, // 允许未定义的环境变量
        abortEarly: false // 报告所有验证错误
      }
    })
  ],
  exports: [NestConfigModule]
})
export class ConfigModule {}
