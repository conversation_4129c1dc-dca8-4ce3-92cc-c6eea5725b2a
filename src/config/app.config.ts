import { registerAs } from '@nestjs/config';

/**
 * 应用程序配置
 */
export default registerAs('app', () => ({
  // 应用程序端口
  port: parseInt(process.env.APP_PORT || '', 10) || 3000,
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '2h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '5d'
  },
  obs: {
    endpoint: process.env.OBS_ENDPOINT,
    domainName: process.env.OBS_DOMAIN_NAME,
    username: process.env.OBS_USERNAME,
    ak: process.env.OBS_AK,
    sk: process.env.OBS_SK,
    bucketName: process.env.OBS_BUCKET
  },
  apiUrl: {
    platform: process.env.PLATFORM_API_URL
  }
}));
