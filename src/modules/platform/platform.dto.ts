import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsOptional, IsString } from 'class-validator';

export class orgsInfoResponseDto {
  @ApiProperty({ description: '数据id' })
  @IsString()
  id: string;

  @ApiProperty({ description: '名称', default: '' })
  @IsString()
  name: string;

  @ApiProperty({ description: '合同章名称' })
  @IsString()
  sealName: string;

  @ApiPropertyOptional({ description: '授权码', default: '' })
  @IsOptional()
  @IsString()
  authCode?: string | null;

  @ApiProperty({ description: '类型 1:公司 2:项目 3:租户根' })
  type: string;

  @ApiPropertyOptional({ description: '是否可见', default: false })
  @IsOptional()
  @IsBoolean()
  isHide?: boolean | null;

  @ApiPropertyOptional({ description: '父级id' })
  @IsOptional()
  @IsString()
  parentId?: string | null;

  @ApiProperty({ description: '全路径id(使用/分隔)', default: '' })
  @IsString()
  fullId: string;

  @ApiProperty({ description: '全名称(使用/分隔)', default: '' })
  @IsString()
  fullName: string;

  @ApiProperty({ description: '级别', default: 1 })
  @IsInt()
  level: number;

  @ApiProperty({ description: '排序', default: 1 })
  @IsInt()
  sort: number;

  @ApiProperty({ description: '是否叶子节点', default: true })
  @IsBoolean()
  isLeaf: boolean;

  @ApiProperty({ description: '创建人', default: 'system' })
  @IsString()
  createBy: string;

  @ApiProperty({ description: '更新人', default: 'system' })
  @IsString()
  updateBy: string;
}
