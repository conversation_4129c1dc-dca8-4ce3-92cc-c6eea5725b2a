import { HttpException, HttpStatus, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { BusinessCostAccountSeedService } from '@/common/modules/prisma/seed/services';

@Injectable()
export class BusinessCostAccountService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly businessCostAccountSeedService: BusinessCostAccountSeedService
  ) {}

  async getList(reqUser: IReqUser) {
    await this.businessCostAccountSeedService.init(this.prisma, reqUser);
    return await this.prisma.accountDictionary.findMany({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      orderBy: {
        sort: 'asc'
      }
    });
  }

  // 修改状态
  async updateStatus(
    tx: PrismaService,
    reqUser: IReqUser,
    id: string,
    status: boolean
  ) {
    await tx.accountDictionary.update({
      where: {
        id
      },
      data: {
        status,
        updateBy: reqUser.id
      }
    });
  }

  async getObj(id: string) {
    const data = await this.prisma.accountDictionary.findFirst({
      where: {
        id,
        isDeleted: false
      }
    });
    if (!data) {
      throw new HttpException('数据不存在', HttpStatus.BAD_REQUEST);
    } else {
      return data;
    }
  }
}
