import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { Dict_Type } from '@/prisma/generated';

import { AccountBusinessCostSubjectService } from './account-business-cost-subject/account-business-cost-subject.service';
import { AccountCostDictionaryService } from './account-cost-dictionary/account-account-cost-dictionary.service';
import { AccountMaterialDictionaryService } from './account-material-dictionary/account-material-dictionary.service';
import { AccountMachineryDictionaryService } from './account-mechinery-dictionary/account-account-mechinery-dictionary.service';
import { AccountTaxRateDictionaryService } from './account-taxratel-dictionary/account-taxratel-dictionary.service';
import {
  CreateAccounttDto,
  CreateBusinessCostAccountDto,
  DeteteDictionaryDetailtype,
  DictionaryCategorytype,
  DictionaryDetailtype,
  DictionaryType
} from './business-cost-account.dto';
import { BusinessCostAccountService } from './business-cost-account.service';

@ApiTags('成本核算')
@Controller('business-cost-account')
export class BusinessCostAccountController {
  constructor(
    private readonly service: BusinessCostAccountService,
    private readonly accountBusinessCostSubjectService: AccountBusinessCostSubjectService,
    private readonly accountMaterialDictionaryService: AccountMaterialDictionaryService,
    private readonly accountMachineryDictionaryService: AccountMachineryDictionaryService,
    private readonly accountCostDictionaryService: AccountCostDictionaryService,
    private readonly accountTaxRateDictionaryService: AccountTaxRateDictionaryService
  ) {}

  @ApiOperation({
    summary: '查询左侧列表',
    description: '查询左侧列表'
  })
  @ApiResponse({ status: 200, description: '查询左侧列表成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async list(@ReqUser() reqUser: IReqUser) {
    return await this.service.getList(reqUser);
  }

  @ApiOperation({
    summary: '获取某模块下的版本列表（选择时的版本列表）',
    description: '获取某模块下的版本列表（选择时的版本列表）'
  })
  @ApiResponse({
    status: 200,
    description: '获取某模块下的版本列表（选择时的版本列表）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/version/list')
  async versionList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DictionaryType
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.BUSINESS_COST_SUBJECT:
        return await this.accountBusinessCostSubjectService.getVersionList(
          req,
          reqUser,
          data
        );

      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.getVersionList(
          req,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.getVersionList(
          req,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.getVersionList(
          req,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.getVersionList(
          req,
          reqUser,
          data
        );

      default:
        break;
    }
  }

  @ApiOperation({
    summary: '获取某模块下的分类列表（选择时的分类列表）',
    description: '获取某模块下的分类列表（选择时的分类列表）'
  })
  @ApiResponse({
    status: 200,
    description: '获取某模块下的分类列表（选择时的分类列表）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/category/list')
  async categoryList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DictionaryCategorytype
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.BUSINESS_COST_SUBJECT:
        return await this.accountBusinessCostSubjectService.getCategoryList(
          reqUser,
          data
        );

      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.getCategoryList(
          req,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.getCategoryList(
          req,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.getCategoryList(
          req,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.getCategoryList(
          req,
          reqUser,
          data
        );

      default:
        break;
    }
  }

  @ApiOperation({
    summary: '获取某模块下的明细列表（选择时的明细列表）',
    description: '获取某模块下的明细列表（选择时的明细列表）'
  })
  @ApiResponse({
    status: 200,
    description: '获取某模块下的明细列表（选择时的明细列表）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/detail/list')
  async detailList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DictionaryDetailtype
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.BUSINESS_COST_SUBJECT:
        return await this.accountBusinessCostSubjectService.getDetailList(
          reqUser,
          data
        );

      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.getDetailList(
          req,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.getDetailList(
          req,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.getDetailList(
          req,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.getDetailList(
          req,
          reqUser,
          data
        );

      default:
        break;
    }
  }

  @ApiOperation({
    summary: '业务成本科目配置',
    description: '业务成本科目配置'
  })
  @ApiResponse({
    status: 200,
    description: '业务成本科目配置成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/cost/add')
  async costAdd(@ReqUser() reqUser: IReqUser, @Body() data: CreateAccounttDto) {
    return await this.accountBusinessCostSubjectService.add(reqUser, data);
  }

  @ApiOperation({
    summary: '其他字典配置',
    description: '其他字典配置'
  })
  @ApiResponse({
    status: 200,
    description: '其他字典配置成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/other/add')
  async otherAdd(
    @ReqUser() reqUser: IReqUser,
    @Body() data: CreateBusinessCostAccountDto
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.add(reqUser, data);
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.add(reqUser, data);
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.add(reqUser, data);
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.add(reqUser, data);
      default:
        break;
    }
  }

  @ApiOperation({
    summary: '获取某模块下的版本列表（已选择的版本列表，版本加分类）',
    description: '获取某模块下的版本列表（已选择的版本列表，版本加分类）'
  })
  @ApiResponse({
    status: 200,
    description: '获取某模块下的版本列表（已选择的版本列表）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/choose/version/list')
  async chooseVersionList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DictionaryType
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.BUSINESS_COST_SUBJECT:
        return await this.accountBusinessCostSubjectService.getchooseVersionList(
          req,
          reqUser,
          data
        );
      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.getchooseVersionList(
          req,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.getchooseVersionList(
          req,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.getchooseVersionList(
          req,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.getchooseVersionList(
          req,
          reqUser,
          data
        );

      default:
        break;
    }
  }

  @ApiOperation({
    summary: '获取某个设置下的明细列表（已选择的明细列表）',
    description: '获取某个设置下的明细列表（已选择的明细列表）'
  })
  @ApiResponse({
    status: 200,
    description: '获取某个设置下的明细列表（已选择的明细列表）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/choose/detail/list')
  async chooseDetailList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DictionaryDetailtype
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.getchooseDetailList(
          req,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.getchooseDetailList(
          req,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.getchooseDetailList(
          req,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.getchooseDetailList(
          req,
          reqUser,
          data
        );

      default:
        break;
    }
  }

  @ApiOperation({
    summary: '删除某模块下已经绑定的字典项（根据版本id删除）',
    description: '删除某模块下已经绑定的字典项（根据版本id删除）'
  })
  @ApiResponse({
    status: 200,
    description: '删除某模块下已经绑定的字典项（根据版本id删除）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/version/delete/:versionId')
  async deleteVersion(
    @Param('versionId') versionId: string,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DictionaryType
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      // 业务成本科目
      case Dict_Type.BUSINESS_COST_SUBJECT:
        return await this.accountBusinessCostSubjectService.delete(
          versionId,
          reqUser,
          data
        );
      // 税率字典
      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.deleteVersion(
          versionId,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.deleteVersion(
          versionId,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.deleteVersion(
          versionId,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.deleteVersion(
          versionId,
          reqUser,
          data
        );

      default:
        break;
    }
  }

  @ApiOperation({
    summary: '删除某模块下已经绑定的字典项（根据分类id删除）',
    description: '删除某模块下已经绑定的字典项（根据分类id删除）'
  })
  @ApiResponse({
    status: 200,
    description: '删除某模块下已经绑定的字典项（根据分类id删除）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/category/delete/:categoryId')
  async deleteCategory(
    @Param('categoryId') categoryId: string,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DictionaryCategorytype
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.deleteCategory(
          categoryId,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.deleteCategory(
          categoryId,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.deleteCategory(
          categoryId,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.deleteCategory(
          categoryId,
          reqUser,
          data
        );

      default:
        break;
    }
  }

  @ApiOperation({
    summary: '删除某模块下已经绑定的字典项（根据明细id删除）',
    description: '删除某模块下已经绑定的字典项（根据明细id删除）'
  })
  @ApiResponse({
    status: 200,
    description: '删除某模块下已经绑定的字典项（根据明细id删除）成功'
  })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/detail/delete/:detailId')
  async deleteDetail(
    @Param('detailId') detailId: string,
    @ReqUser() reqUser: IReqUser,
    @Query() data: DeteteDictionaryDetailtype
  ) {
    const obj = await this.service.getObj(data.accountDictionaryId);
    switch (obj.type) {
      case Dict_Type.MATERIAL_DICTIONARY:
        return await this.accountMaterialDictionaryService.deleteDetail(
          detailId,
          reqUser,
          data
        );
      case Dict_Type.MECHANICAL_DICTIONARY:
        return await this.accountMachineryDictionaryService.deleteDetail(
          detailId,
          reqUser,
          data
        );
      case Dict_Type.COST_DICTIONARY:
        return await this.accountCostDictionaryService.deleteDetail(
          detailId,
          reqUser,
          data
        );
      case Dict_Type.TAXRATE_DICTIONARY:
        return await this.accountTaxRateDictionaryService.deleteDetail(
          detailId,
          reqUser,
          data
        );

      default:
        break;
    }
  }
}
