import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';

import {
  CreateAccounttDto,
  DictionaryCategorytype,
  DictionaryDetailtype,
  DictionaryType
} from '../business-cost-account.dto';
import { BusinessCostAccountService } from '../business-cost-account.service';

@Injectable()
export class AccountBusinessCostSubjectService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly service: BusinessCostAccountService,
    private readonly platformService: PlatformService
  ) {}

  async getVersionList(req: Request, reqUser: IReqUser, data: DictionaryType) {
    const { accountDictionaryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    return await this.prisma.$queryRaw<any[]>`
      SELECT 
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        o.name AS org_name,
        bcsv.status,
        EXISTS (
          SELECT 1 
          FROM account_business_cost_subject_version adv
          WHERE adv.tenant_id = bcsv.tenant_id
            AND adv.org_id = ${reqUser.orgId}
            AND adv.version_id = bcsv.id
            AND adv.account_dictionary_id = ${accountDictionaryId}
            AND adv.is_deleted = false
        ) AS is_use
      FROM business_cost_subject_version bcsv
      JOIN platform_meta.org AS o
        ON o.tenant_id = bcsv.tenant_id
        AND o.id = bcsv.org_id
        AND o.is_deleted = false
      WHERE bcsv.is_deleted = false 
        AND bcsv.status != 'NOT_ENABLED' 
        AND bcsv.tenant_id = ${reqUser.tenantId}
        AND bcsv.org_id = ANY(${topOrg}::text[])
      ORDER BY bcsv.create_by DESC
    `;
  }

  async getCategoryList(reqUser: IReqUser, data: DictionaryCategorytype) {
    const { versionId } = data;
    return await this.prisma.$queryRaw<any[]>`
         SELECT
          bcsv.id,
          bcsv.tenant_id,
          bcsv.org_id,
          bcsv.name,
          bcsv.code,
          bcsv.parent_id,
          bcsv.remark,
          bcsv.business_cost_subject_version_id as version_id
        FROM business_cost_subject_category bcsv
        WHERE bcsv.is_deleted = false
          AND bcsv.tenant_id = ${reqUser.tenantId}
          AND bcsv.business_cost_subject_version_id = ${versionId}
          AND bcsv.is_deleted = false
        ORDER BY bcsv."level", bcsv.sort
        `;
  }

  async getDetailList(reqUser: IReqUser, data: DictionaryDetailtype) {
    const { versionId, categoryId } = data;
    const res = await this.prisma.$queryRaw<any[]>`
      SELECT
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        bcsv.business_cost_subject_version_id as version_id,
        bcsv.business_cost_subject_category_id as category_id,
        bcsv.financial_cost_subject_id,
        bcsv.code,
        bcsv.unit,
        bcsv.expense_category,
        bcsv.accounting_description,
        bcsv.is_safety_construction_fee,
        bcsv.subject_mapping_description
      FROM business_cost_subject_detail bcsv
      WHERE bcsv.is_deleted = false
        AND bcsv.tenant_id = ${reqUser.tenantId}
        AND bcsv.business_cost_subject_version_id = ${versionId}
        AND bcsv.business_cost_subject_category_id = ${categoryId}
        AND bcsv.is_active = true
      ORDER BY bcsv.create_at DESC
    `;
    return res;
  }

  async add(reqUser: IReqUser, data: CreateAccounttDto) {
    await this.prisma.$transaction(async (tx) => {
      await tx.accountBusinessCostSubjectVersion.create({
        data: {
          ...data,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }
      });
      // 修改配置状态为已配置
      await this.service.updateStatus(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId,
        true
      );
    });
    return true;
  }

  async getchooseVersionList(
    req: Request,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    // 1、获取版本
    const version = await this.prisma.$queryRaw<any[]>`
      select abcsv.version_id as id, null as parent_id, abcsv.id as data_id, bcsv.name, abcsv.tenant_id, abcsv.org_id 
        from account_business_cost_subject_version abcsv
        join business_cost_subject_version bcsv 
          on bcsv.id = abcsv.version_id
          AND bcsv.tenant_id = abcsv.tenant_id
          AND bcsv.org_id = ANY(${topOrg}::text[])
          AND bcsv.is_deleted = false
          AND bcsv.status != 'NOT_ENABLED'
        where abcsv.is_deleted = false 
          AND abcsv.account_dictionary_id = ${data.accountDictionaryId}
          AND abcsv.tenant_id = ${reqUser.tenantId}
          AND abcsv.org_id = ${reqUser.orgId}
        order by bcsv.create_by desc
    `;
    // 获取版本下id集合
    const versionIds = version.map((item) => item.id);
    // 查询已绑定的分类
    const accountCategory =
      await this.prisma.businessCostSubjectCategory.findMany({
        select: {
          id: true
        },
        where: {
          tenantId: reqUser.tenantId,
          // orgId: reqUser.orgId,
          isDeleted: false,
          businessCostSubjectVersionId: {
            in: versionIds
          }
        }
      });
    // 查询分类
    const category: any[] =
      await this.prisma.businessCostSubjectCategory.findMany({
        select: {
          id: true,
          name: true,
          parentId: true,
          code: true,
          remark: true,
          businessCostSubjectVersionId: true
        },
        where: {
          businessCostSubjectVersionId: {
            in: versionIds
          },
          id: {
            in: accountCategory.map((item) => item.id)
          },
          isDeleted: false
        },
        orderBy: [
          {
            level: 'asc'
          },
          {
            sort: 'asc'
          }
        ]
      });
    // 将版本和分类进行关联
    category.map((category) => {
      category['versionId'] = category.businessCostSubjectVersionId;
      delete category.businessCostSubjectVersionId;
      const categoryVersion = version.find(
        (version) =>
          version.id === category.versionId && category.parentId === null
      );
      category.parentId = categoryVersion
        ? categoryVersion.id
        : category.parentId;
    });
    return [...version, ...category];
  }

  async delete(versionId: string, reqUser: IReqUser, data: DictionaryType) {
    // 缺少删除的引用判断，后续补上
    await this.prisma.$transaction(async (tx) => {
      await tx.accountBusinessCostSubjectVersion.updateMany({
        where: {
          versionId,
          isDeleted: false,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          accountDictionaryId: data.accountDictionaryId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 修改配置状态为未配置
      await this.service.updateStatus(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId,
        false
      );
    });
    return true;
  }
}
