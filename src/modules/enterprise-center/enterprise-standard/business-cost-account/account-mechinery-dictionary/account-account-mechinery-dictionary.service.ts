import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { Prisma } from '@/prisma/generated';

import {
  CreateBusinessCostAccountDetailDto,
  CreateBusinessCostAccountDto,
  DeteteDictionaryDetailtype,
  DictionaryCategorytype,
  DictionaryDetailtype,
  DictionaryType
} from '../business-cost-account.dto';
import { BusinessCostAccountService } from '../business-cost-account.service';

@Injectable()
export class AccountMachineryDictionaryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly service: BusinessCostAccountService,
    private readonly platformService: PlatformService
  ) {}

  async getVersionList(req: Request, reqUser: IReqUser, data: DictionaryType) {
    const { accountDictionaryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    return await this.prisma.$queryRaw<any[]>`
        SELECT 
          bcsv.id,
          bcsv.tenant_id,
          bcsv.org_id,
          bcsv.business_cost_subject_version_id,
          bcsv.name,
          o.name AS org_name,
          bcsv.status,
          EXISTS (
            SELECT 1 
            FROM account_mechinery_dictionary_version adv
            WHERE adv.tenant_id = bcsv.tenant_id
              AND adv.org_id = ${reqUser.orgId}
              AND adv.version_id = bcsv.id
              AND adv.account_dictionary_id = ${accountDictionaryId}
              AND adv.is_deleted = false
          ) AS is_use
        FROM machinery_dictionary_version bcsv
        JOIN platform_meta.org AS o
          ON o.tenant_id = bcsv.tenant_id
          AND o.id = bcsv.org_id
          AND o.is_deleted = false
        WHERE bcsv.is_deleted = false 
          AND bcsv.status != 'NOT_ENABLED' 
          AND bcsv.tenant_id = ${reqUser.tenantId}
          AND bcsv.org_id = ANY(${topOrg}::text[])
        ORDER BY bcsv.create_by DESC
      `;
  }

  async getCategoryList(
    req: Request,
    reqUser: IReqUser,
    data: DictionaryCategorytype
  ) {
    const { versionId, accountDictionaryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    return await this.prisma.$queryRaw<any[]>`
       SELECT
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        bcsv.code,
        bcsv.parent_id,
        bcsv.remark,
        bcsv.machinery_dictionary_version_id as version_id,
        EXISTS (
          SELECT 1
          FROM account_mechinery_dictionary_category adv
          WHERE adv.tenant_id = bcsv.tenant_id
            AND adv.org_id = ${reqUser.orgId}
            AND adv.account_dictionary_id = ${accountDictionaryId}
            AND adv.version_id = bcsv.machinery_dictionary_version_id
            AND adv.category_id = bcsv.id
            AND adv.is_deleted = false
        ) AS is_use
      FROM machinery_dictionary_category bcsv
      WHERE bcsv.is_deleted = false
        AND bcsv.tenant_id = ${reqUser.tenantId}
        AND bcsv.org_id = ANY(${topOrg}::text[])
        AND bcsv.machinery_dictionary_version_id = ${versionId}
      ORDER BY bcsv."level", bcsv.sort
      `;
  }

  async getDetailList(
    req: Request,
    reqUser: IReqUser,
    data: DictionaryDetailtype
  ) {
    const { versionId, categoryId, accountDictionaryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    const res = await this.prisma.$queryRaw<any[]>`
        SELECT 
          bcsv.*,
          COALESCE(STRING_AGG(mdbcsd.business_cost_subject_detail_id, ','), '') AS business_cost_subject_detail_ids
        FROM (
          SELECT
            bcsv.id,
            bcsv.tenant_id,
            bcsv.org_id,
            bcsv.name,
            bcsv.machinery_dictionary_version_id as version_id,
            bcsv.machinery_dictionary_category_id as category_id,
            bcsv.code,
            bcsv.specification_model,
            bcsv.remark,
            bcsv.account_explanation,
            EXISTS (
              SELECT 1
              FROM account_mechinery_dictionary_detail adv
              WHERE adv.tenant_id = bcsv.tenant_id
                AND adv.org_id = ${reqUser.orgId}
                AND adv.version_id = bcsv.machinery_dictionary_version_id
                AND adv.account_dictionary_id = ${accountDictionaryId}
                AND adv.category_id = bcsv.machinery_dictionary_category_id
                AND adv.detail_id = bcsv.id
                AND adv.is_deleted = false
            ) AS is_use
          FROM machinery_dictionary_detail bcsv
          WHERE bcsv.is_deleted = false
            AND bcsv.tenant_id = ${reqUser.tenantId}
            AND bcsv.org_id = ANY(${topOrg}::text[])
            AND bcsv.machinery_dictionary_version_id = ${versionId}
            AND bcsv.machinery_dictionary_category_id = ${categoryId}
            AND bcsv.is_active = true
          ORDER BY bcsv.create_at DESC
        ) bcsv
        LEFT JOIN machinery_detail_business_cost_subject_detail mdbcsd 
          ON mdbcsd.machinery_dictionary_detail_id = bcsv.id
          AND mdbcsd.tenant_id = bcsv.tenant_id
          AND mdbcsd.org_id = bcsv.org_id
          AND mdbcsd.is_deleted = false
        GROUP BY 
          bcsv.id, 
          bcsv.tenant_id,
          bcsv.org_id,
          bcsv.name,
          bcsv.version_id,
          bcsv.category_id,
          bcsv.code,
          bcsv.specification_model,
          bcsv.remark,
          bcsv.account_explanation,
          bcsv.is_use
        ;
      `;
    return res.map((item) => {
      return {
        ...item,
        businessCostSubjectDetailIds:
          item.businessCostSubjectDetailIds.split(',')
      };
    });
  }

  async add(reqUser: IReqUser, data: CreateBusinessCostAccountDto) {
    const { accountDictionaryId, versionId, categoryIds, details } = data;
    await this.prisma.$transaction(async (tx) => {
      // 新增版本
      await this.createVersion(
        tx as PrismaService,
        reqUser,
        accountDictionaryId,
        versionId
      );
      // 新增分类
      await this.createCategory(
        tx as PrismaService,
        reqUser,
        accountDictionaryId,
        versionId,
        categoryIds
      );
      // 新增明细
      await this.createDetail(
        tx as PrismaService,
        reqUser,
        accountDictionaryId,
        versionId,
        details
      );
      // 修改配置状态为已配置
      await this.service.updateStatus(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId,
        true
      );
    });
    return true;
  }

  /**
   * 新增版本
   * @param tx
   * @param reqUser
   * @param accountDictionaryId
   * @param versionId
   */
  async createVersion(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string
  ) {
    // 查询是否已存在版本
    const version = await tx.accountMechineryDictionaryVersion.findFirst({
      where: {
        accountDictionaryId,
        versionId: versionId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
    if (!version) {
      await tx.accountMechineryDictionaryVersion.create({
        data: {
          accountDictionaryId,
          versionId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }
      });
    }
  }

  /**
   * 新增分类
   * @param tx
   * @param reqUser
   * @param versionId
   * @param categoryIds
   */
  async createCategory(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string,
    categoryIds: string[]
  ) {
    // 查询存在的分类
    const existCategoryIds =
      await tx.accountMechineryDictionaryCategory.findMany({
        where: {
          accountDictionaryId,
          categoryId: {
            in: categoryIds
          },
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          versionId: versionId,
          isDeleted: false
        },
        select: {
          categoryId: true
        }
      });
    // 过滤掉已存在的
    categoryIds = categoryIds.filter(
      (categoryId) =>
        !existCategoryIds.find(
          (existCategory) => existCategory.categoryId === categoryId
        )
    );
    await tx.accountMechineryDictionaryCategory.createMany({
      data: categoryIds.map((categoryId) => ({
        categoryId: categoryId,
        versionId: versionId,
        accountDictionaryId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }))
    });
  }

  /**
   * 新增明细
   * @param tx
   * @param reqUser
   * @param accountDictionaryId
   * @param versionId
   * @param data
   */
  async createDetail(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string,
    data: CreateBusinessCostAccountDetailDto[]
  ) {
    await tx.accountMechineryDictionaryDetail.createMany({
      data: data.map((item) => ({
        ...item,
        accountDictionaryId,
        versionId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }))
    });
  }

  async getchooseVersionList(
    req: Request,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    // 1、获取版本 有business_cost_subject_version_id
    const version = await this.prisma.$queryRaw<any[]>`
        select abcsv.version_id as id, null as parent_id, abcsv.id as data_id, bcsv.name, abcsv.tenant_id, abcsv.org_id, bcsv.business_cost_subject_version_id 
          from account_mechinery_dictionary_version abcsv
          join machinery_dictionary_version bcsv 
            on bcsv.id = abcsv.version_id
            AND bcsv.tenant_id = abcsv.tenant_id
            AND bcsv.org_id = ANY(${topOrg}::text[])
            AND bcsv.is_deleted = false
            AND bcsv.status != 'NOT_ENABLED'
          where abcsv.is_deleted = false 
            AND abcsv.account_dictionary_id = ${data.accountDictionaryId}
            AND abcsv.tenant_id = ${reqUser.tenantId}
            AND abcsv.org_id = ${reqUser.orgId}
          order by bcsv.create_by desc
      `;
    // 没有business_cost_subject_version_id
    //   const version = await this.prisma.$queryRaw<any[]>`
    //   select abcsv.version_id as id, null as parent_id, abcsv.id as data_id, bcsv.name, abcsv.tenant_id, abcsv.org_id
    //     from account_material_dictionary_version abcsv
    //     join material_dictionary_version bcsv
    //       on bcsv.id = abcsv.version_id
    //       AND bcsv.tenant_id = abcsv.tenant_id
    //       AND bcsv.org_id = abcsv.org_id
    //       AND bcsv.is_deleted = false
    //       AND bcsv.status != 'NOT_ENABLED'
    //     where abcsv.is_deleted = false
    //       AND abcsv.account_dictionary_id = ${data.accountDictionaryId}
    //       AND abcsv.tenant_id = ${reqUser.tenantId}
    //       AND abcsv.org_id = ${reqUser.orgId}
    //     order by bcsv.create_by desc
    // `;
    // 获取版本下id集合
    const versionIds = version.map((item) => item.id);
    // 查询已绑定的分类
    const accountCategory =
      await this.prisma.accountMechineryDictionaryCategory.findMany({
        select: {
          categoryId: true
        },
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          versionId: {
            in: versionIds
          }
        }
      });
    // 查询分类 没有business_cost_subject_version_id
    const category: any[] =
      await this.prisma.machineryDictionaryCategory.findMany({
        select: {
          id: true,
          name: true,
          parentId: true,
          code: true,
          remark: true,
          machineryDictionaryVersionId: true,
          machineryDictionaryVersion: {
            select: {
              businessCostSubjectVersionId: true
            }
          }
        },
        where: {
          machineryDictionaryVersionId: {
            in: versionIds
          },
          id: {
            in: accountCategory.map((item) => item.categoryId)
          },
          isDeleted: false
        },
        orderBy: [
          {
            level: 'asc'
          },
          {
            sort: 'asc'
          }
        ]
      });
    // 将版本和分类进行关联
    category.map((category) => {
      category['versionId'] = category.machineryDictionaryVersionId;
      category['businessCostSubjectVersionId'] =
        category.machineryDictionaryVersion.businessCostSubjectVersionId;
      delete category.machineryDictionaryVersion;
      delete category.machineryDictionaryVersionId;
      const categoryVersion = version.find(
        (version) =>
          version.id === category.versionId && category.parentId === null
      );
      category.parentId = categoryVersion
        ? categoryVersion.id
        : category.parentId;
    });
    return [...version, ...category];
  }

  async getchooseDetailList(
    req: Request,
    reqUser: IReqUser,
    data: DictionaryDetailtype
  ) {
    const { accountDictionaryId, versionId, categoryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    let categoryIds: string[] = [];
    if (categoryId) {
      // 查询该分类id下的所有叶子级id
      const category = await this.prisma.$queryRaw<any[]>`
        with RECURSIVE temp_category as (
          select id, name, is_leaf, is_active from machinery_dictionary_category 
          where is_deleted = false 
          and tenant_id = ${reqUser.tenantId}
          and org_id =ANY(${topOrg}::text[])
          and id = ${categoryId}
          and is_active = true
          
          UNION ALL
          
          select child.id, child.name, child.is_leaf, child.is_active from machinery_dictionary_category as child
          join temp_category tc on tc.id = child.parent_id
          where child.is_deleted = false 
          and child.is_active = true
        )
        select tm.id from temp_category tm
        join account_mechinery_dictionary_category amdc
        on amdc.category_id = tm.id 
        and amdc.is_deleted = false 
        and amdc.account_dictionary_id = ${accountDictionaryId}
        and amdc.tenant_id = ${reqUser.tenantId}
        and amdc.org_id = ${reqUser.orgId}
        where tm.is_leaf = true
      `;
      categoryIds = category.map((item) => item.id);
    }
    const res = await this.prisma.$queryRaw<any[]>`
      SELECT 
        bcsv.*,
        COALESCE(STRING_AGG(mdbcsd.business_cost_subject_detail_id, ','), '') AS business_cost_subject_detail_ids
      FROM (
        select mde.id, amde.category_id, amde.version_id, 
          mde.name, mde.code, mde.specification_model, 
          mde.remark, mde.account_explanation, mde.tenant_id, mde.org_id  
        from account_mechinery_dictionary_detail amde 
        join machinery_dictionary_detail mde
        on mde.id = amde.detail_id
          and mde.is_deleted = false
          and mde.is_active = true
          and mde.org_id = ANY(${topOrg}::text[])
          and mde.tenant_id = ${reqUser.tenantId}
        where amde.is_deleted = false
          ${versionId ? Prisma.sql`and amde.version_id = ${versionId}` : Prisma.empty}
          ${categoryIds.length ? Prisma.sql`and amde.category_id in (${categoryIds.join(',')})` : Prisma.empty}
          and amde.account_dictionary_id = ${accountDictionaryId}
          and amde.tenant_id = ${reqUser.tenantId}
          and amde.org_id = ${reqUser.orgId}
        order by amde.category_id, mde.sort
      ) bcsv
      LEFT JOIN machinery_detail_business_cost_subject_detail mdbcsd 
        ON mdbcsd.machinery_dictionary_detail_id = bcsv.id
        AND mdbcsd.tenant_id = bcsv.tenant_id
        AND mdbcsd.org_id = bcsv.org_id
        AND mdbcsd.is_deleted = false
      GROUP BY 
        bcsv.id, 
        bcsv.name,
        bcsv.version_id,
        bcsv.category_id,
        bcsv.code,
        bcsv.specification_model,
        bcsv.remark,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.account_explanation
    `;
    return res.map((item) => {
      return {
        ...item,
        businessCostSubjectDetailIds:
          item.businessCostSubjectDetailIds.split(',')
      };
    });
  }

  async deleteVersion(
    versionId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    await this.prisma.$transaction(async (tx) => {
      // 删除版本
      await this.removeVerisonByVersionId(
        tx as PrismaService,
        versionId,
        reqUser,
        data.accountDictionaryId
      );
      // 删除分类
      await this.removeCategoryByVersionId(
        tx as PrismaService,
        versionId,
        reqUser,
        data
      );
      // 删除明细
      await this.removeDetailByVersionId(
        tx as PrismaService,
        versionId,
        reqUser,
        data
      );
      // 查询是否还有其他版本的引用
      const version = await tx.accountMechineryDictionaryVersion.findFirst({
        where: {
          accountDictionaryId: data.accountDictionaryId,
          isDeleted: false,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId
        }
      });
      if (!version) {
        // 修改配置状态为未配置
        await this.service.updateStatus(
          tx as PrismaService,
          reqUser,
          data.accountDictionaryId,
          false
        );
      }
    });
    return true;
  }

  async deleteCategory(
    categoryId: string,
    reqUser: IReqUser,
    data: DictionaryCategorytype
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    await this.prisma.$transaction(async (tx) => {
      // 删除分类
      await this.removeCategoryByCategoryId(
        tx as PrismaService,
        categoryId,
        reqUser,
        data.accountDictionaryId
      );
      // 删除明细
      await this.removeDetailByCategoryId(
        tx as PrismaService,
        categoryId,
        reqUser,
        data
      );
      // 查询该版本下是否还存在其他分类
      await this.getCategoryByVersionId(
        tx as PrismaService,
        data.versionId,
        reqUser,
        data.accountDictionaryId
      );
      // 查询是否还有其他版本的引用
      await this.getVersionByAccountDictionaryId(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId
      );
    });
    return true;
  }

  async deleteDetail(
    detailId: string,
    reqUser: IReqUser,
    data: DeteteDictionaryDetailtype
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    await this.prisma.$transaction(async (tx) => {
      // 删除明细
      await this.removeDetailByDetailId(
        tx as PrismaService,
        detailId,
        reqUser,
        data.accountDictionaryId,
        data.versionId,
        data.categoryId
      );
      // 查询该版本分类下是否还有明细
      await this.getDetailByCategoryIdAndVersionId(
        tx as PrismaService,
        data.versionId,
        data.categoryId,
        detailId,
        reqUser,
        data.accountDictionaryId
      );
      // 查询该版本下是否还存在其他分类
      await this.getCategoryByVersionId(
        tx as PrismaService,
        data.versionId,
        reqUser,
        data.accountDictionaryId
      );
      // 查询是否还有其他版本的引用
      await this.getVersionByAccountDictionaryId(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId
      );
    });
    return true;
  }

  // 查询该版本分类下是否还有明细
  async getDetailByCategoryIdAndVersionId(
    tx: PrismaService,
    versionId: string,
    categoryId: string,
    detailId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    const detailList = await tx.accountMechineryDictionaryDetail.findFirst({
      select: {
        id: true
      },
      where: {
        accountDictionaryId: accountDictionaryId,
        versionId,
        categoryId,
        detailId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    if (!detailList) {
      // 若不存在，删除分类
      return await this.removeCategoryByCategoryId(
        tx,
        categoryId,
        reqUser,
        accountDictionaryId
      );
    }
  }

  // 查询该版本下是否还存在其他分类
  async getCategoryByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    const categoryList =
      await this.prisma.accountMechineryDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          isDeleted: false,
          versionId: versionId,
          accountDictionaryId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId
        }
      });
    if (!categoryList) {
      // 如果该版本下已没有其他分类，则删除该版本
      await this.removeVerisonByVersionId(
        tx,
        versionId,
        reqUser,
        accountDictionaryId
      );
    }
  }

  // 查询是否还有其他版本的引用
  async getVersionByAccountDictionaryId(
    tx: PrismaService,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    const version = await tx.accountMechineryDictionaryVersion.findFirst({
      where: {
        accountDictionaryId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
    if (!version) {
      // 修改配置状态为未配置
      await this.service.updateStatus(tx, reqUser, accountDictionaryId, false);
    }
  }

  // 根据明细id删除明细
  async removeDetailByDetailId(
    tx: PrismaService,
    detailId: string,
    reqUser: IReqUser,
    accountDictionaryId: string,
    versionId: string,
    categoryId: string
  ) {
    // 缺少删除的引用判断，后续补上（查询明细是否被引用，已引用的不可删除）
    return await tx.accountMechineryDictionaryDetail.updateMany({
      where: {
        detailId,
        isDeleted: false,
        accountDictionaryId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        versionId,
        categoryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据分类id删除分类
  async removeCategoryByCategoryId(
    tx: PrismaService,
    categoryId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    return await tx.accountMechineryDictionaryCategory.updateMany({
      where: {
        categoryId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据分类id删除明细
  async removeDetailByCategoryId(
    tx: PrismaService,
    categoryId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    return await tx.accountMechineryDictionaryDetail.updateMany({
      where: {
        categoryId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: data.accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 删除版本
  async removeVerisonByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    accountDictionaryId: string
  ) {
    return await tx.accountMechineryDictionaryVersion.updateMany({
      where: {
        versionId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据版本删除分类
  async removeCategoryByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    return await tx.accountMechineryDictionaryCategory.updateMany({
      where: {
        versionId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: data.accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 根据版本删除明细
  async removeDetailByVersionId(
    tx: PrismaService,
    versionId: string,
    reqUser: IReqUser,
    data: DictionaryType
  ) {
    return await tx.accountMechineryDictionaryDetail.updateMany({
      where: {
        versionId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        accountDictionaryId: data.accountDictionaryId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
