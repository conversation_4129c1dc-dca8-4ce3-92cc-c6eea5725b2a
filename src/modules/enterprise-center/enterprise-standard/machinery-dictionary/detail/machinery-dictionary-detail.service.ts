import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { isEmpty as _isEmpty } from 'lodash';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus } from '@/prisma/generated';
import { MachineryDictionaryDetailWhereInput } from '@/prisma/generated/models';

import {
  MachineryDictionaryDetailCreateDto,
  MachineryDictionaryDetailQueryParamDto,
  MachineryDictionaryDetailSearchListDto,
  MachineryDictionaryDetailUpdateDto
} from './machinery-dictionary-detail.dto';

@Injectable()
export class MachineryDictionaryDetailService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    queryParam: MachineryDictionaryDetailQueryParamDto
  ) {
    const { tenantId, orgId } = reqUser;
    const { machineryDictionaryVersionId, machineryDictionaryCategoryId } =
      queryParam;

    const where: MachineryDictionaryDetailWhereInput = {
      tenantId,
      orgId,
      machineryDictionaryVersionId,
      isDeleted: false
    };
    // 查询所有明细时，不按分类进行过滤
    if (machineryDictionaryCategoryId !== '100') {
      where.machineryDictionaryCategoryId = machineryDictionaryCategoryId;
    }
    const list = await this.findList(where);

    return list;
  }

  async search(
    reqUser: IReqUser,
    query: MachineryDictionaryDetailSearchListDto
  ) {
    const { tenantId, orgId } = reqUser;
    const { machineryDictionaryVersionId, name } = query;

    const list = await this.findList({
      tenantId,
      orgId,
      machineryDictionaryVersionId,
      ...(name && { name: { contains: name } }),
      isDeleted: false
    });

    return list;
  }

  private async findList(where: MachineryDictionaryDetailWhereInput) {
    const list = await this.prisma.machineryDictionaryDetail.findMany({
      select: {
        id: true,
        machineryDictionaryVersionId: true,
        machineryDictionaryCategoryId: true,
        code: true,
        name: true,
        specificationModel: true,
        // meteringUnit: true,
        // type: true,
        remark: true,
        accountExplanation: true,
        isActive: true,
        sort: true,
        machineryDictionaryCategory: {
          select: {
            isActive: true
          }
        },
        machineryDetailBusinessCostSubjectDetail: {
          select: {
            businessCostSubjectDetailId: true
          },
          where: {
            isDeleted: false
          }
        }
      },
      where,
      orderBy: [
        { machineryDictionaryCategory: { isActive: 'desc' } },
        { machineryDictionaryCategory: { level: 'asc' } },
        { machineryDictionaryCategory: { sort: 'asc' } },
        { isActive: 'desc' },
        { sort: 'asc' }
      ]
    });

    return list.map((item) => {
      return {
        ...item,
        businessCostSubjectDetailsIds:
          item.machineryDetailBusinessCostSubjectDetail.map(
            (id) => id.businessCostSubjectDetailId
          )
      };
    });
  }

  async createOne(reqUser: IReqUser, data: MachineryDictionaryDetailCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.machineryDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data });

    const maxSort = await this.prisma.machineryDictionaryDetail.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryVersionId: data.machineryDictionaryVersionId,
        machineryDictionaryCategoryId: data.machineryDictionaryCategoryId,
        isDeleted: false
      }
    });
    const { businessCostSubjectDetailsIds, ...detailData } = data;
    // 创建明细数据
    const detail = await this.prisma.machineryDictionaryDetail.create({
      data: {
        ...detailData,
        sort: maxSort._max.sort ? maxSort._max.sort + 1 : 1,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });

    // 写入中间表
    if (businessCostSubjectDetailsIds?.length) {
      await this.prisma.machineryDetailBusinessCostSubjectDetail.createMany({
        data: businessCostSubjectDetailsIds.map((item) => {
          return {
            tenantId,
            orgId,
            createBy: userId,
            machineryDictionaryDetailId: item,
            businessCostSubjectDetailId: detail.id,
            updateBy: userId
          };
        })
      });
    }
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.machineryDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.machineryDictionaryDetail.findUnique({
      select: {
        machineryDictionaryVersionId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: MachineryDictionaryDetailUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.machineryDictionaryVersionId);

    await this.checkUnique({ tenantId, orgId, data, id });

    const { businessCostSubjectDetailsIds, ...detailData } = data;

    // 更新数据
    const detail = await this.prisma.machineryDictionaryDetail.update({
      data: {
        ...detailData,
        updateBy: userId
      },
      where: {
        id,
        tenantId,
        orgId
      }
    });

    // 更新中间表数据
    if (businessCostSubjectDetailsIds != null) {
      const existingRelations =
        await this.prisma.machineryDetailBusinessCostSubjectDetail.findMany({
          where: {
            tenantId,
            orgId,
            machineryDictionaryDetailId: detail.id,
            isDeleted: false
          },
          select: {
            businessCostSubjectDetailId: true
          }
        });
      const existingIds = existingRelations.map(
        (item) => item.businessCostSubjectDetailId
      );
      const newIds = businessCostSubjectDetailsIds;

      // 找出需要删除的（存在于 old，但不在 new 中）
      const toDelete = existingIds.filter((id) => !newIds.includes(id));

      // 找出需要新增的（存在于 new，但不在 old 中）
      const toCreate = newIds.filter((id) => !existingIds.includes(id));

      // 删除旧的
      if (toDelete.length > 0) {
        await this.prisma.machineryDetailBusinessCostSubjectDetail.updateMany({
          where: {
            tenantId,
            orgId,
            machineryDictionaryDetailId: detail.id,
            businessCostSubjectDetailId: {
              in: toDelete
            },
            isDeleted: false
          },
          data: {
            isDeleted: true,
            updateBy: userId
          }
        });
      }

      // 新增新的
      if (toCreate.length > 0) {
        await this.prisma.machineryDetailBusinessCostSubjectDetail.createMany({
          data: toCreate.map((item) => ({
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId,
            machineryDictionaryDetailId: detail.id,
            businessCostSubjectDetailId: item
          }))
        });
      }
    }
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.machineryDictionaryVersionId);

    // 检查明细是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('明细被引用，无法删除！');
    }

    // 逻辑删除
    await this.prisma.machineryDictionaryDetail.update({
      data: {
        isDeleted: true,
        updateBy: userId
      },
      where: {
        id,
        tenantId,
        orgId
      }
    });

    // 逻辑删除中间表数据
    await this.prisma.machineryDetailBusinessCostSubjectDetail.updateMany({
      where: {
        tenantId,
        orgId,
        machineryDictionaryDetailId: id
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }

  async moveOne(id: string, moveTo: MoveToEnum, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detailOne = await this.getOne(id);

    if (!detailOne) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(detailOne.machineryDictionaryVersionId);

    const currentRecord = await this.prisma.machineryDictionaryDetail.findFirst(
      {
        select: {
          id: true,
          machineryDictionaryVersionId: true,
          machineryDictionaryCategoryId: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      }
    );
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 同一版本与分类下，检查唯一性
   * 编码唯一，名称+规格型号唯一
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data:
      | MachineryDictionaryDetailCreateDto
      | MachineryDictionaryDetailUpdateDto;
    id?: string;
  }) {
    const { tenantId, orgId, data, id = '' } = args;
    const { code, name, specificationModel } = data;

    // 检查编码唯一性
    const duplicateCode: any[] = await this.prisma.$queryRaw`
      select
        mdd.code
        ,mdc.name as category_name
        ,mdc.code as category_code
      from machinery_dictionary_detail as mdd
      join machinery_dictionary_category as mdc
        on mdc.is_deleted = false
        and mdc.tenant_id = mdd.tenant_id
        and mdc.org_id = mdd.org_id
        and mdd.machinery_dictionary_category_id = mdc.id
      where mdd.is_deleted = false
        and mdd.tenant_id = ${tenantId}
        and mdd.org_id = ${orgId}
        and mdd.machinery_dictionary_version_id = ${data.machineryDictionaryVersionId}
        and mdd.code = ${code}
        and mdd.id <> ${id}
    `;

    if (!_isEmpty(duplicateCode)) {
      const { categoryName, categoryCode, code } = duplicateCode[0];
      throw new BadRequestException(
        `机械字典分类[${categoryCode}-${categoryName}], 已存在编码[${code}]`
      );
    }

    // 检查名称+规格型号唯一性
    const duplicateNameAndSpec: any[] = await this.prisma.$queryRaw`
      select
        mdd.name
        ,mdd.specification_model
        ,mdc.name as category_name
        ,mdc.code as category_code
      from machinery_dictionary_detail as mdd
      join machinery_dictionary_category as mdc
        on mdc.is_deleted = false
        and mdc.tenant_id = mdd.tenant_id
        and mdc.org_id = mdd.org_id
        and mdd.machinery_dictionary_category_id = mdc.id
      where mdd.is_deleted = false
        and mdd.tenant_id = ${tenantId}
        and mdd.org_id = ${orgId}
        and mdd.machinery_dictionary_version_id = ${data.machineryDictionaryVersionId}
        and mdd.id <> ${id}
        and mdd.name = ${name}
        and mdd.specification_model = ${specificationModel}
    `;

    if (!_isEmpty(duplicateNameAndSpec)) {
      const { categoryName, categoryCode, name, specificationModel } =
        duplicateNameAndSpec[0];
      throw new BadRequestException(
        `机械字典分类[${categoryCode}-${categoryName}], 已存在名称=[${name}]&规格型号=[${specificationModel}]`
      );
    }
  }

  /** 上移 */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      machineryDictionaryVersionId: string;
      machineryDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { machineryDictionaryVersionId, machineryDictionaryCategoryId } =
      currentRecord;

    // 找到上一条数据
    const prevRecord = await this.prisma.machineryDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryVersionId,
        machineryDictionaryCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          lt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.machineryDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.machineryDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId,
          id: prevRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /** 下移 */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      machineryDictionaryVersionId: string;
      machineryDictionaryCategoryId: string;
      sort: number;
    }
  ) {
    const { machineryDictionaryVersionId, machineryDictionaryCategoryId } =
      currentRecord;

    // 找到下一条数据
    const nextRecord = await this.prisma.machineryDictionaryDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryVersionId,
        machineryDictionaryCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          gt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.machineryDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          // machineryDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.machineryDictionaryDetail.update({
        where: {
          tenantId,
          orgId,
          // machineryDictionaryVersionId,
          id: nextRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 获取明细项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountMechineryDictionaryDetail.count({
        where: {
          tenantId,
          isDeleted: false,
          detailId: id
        }
      });
    return { projectRefCount };
  }
}
