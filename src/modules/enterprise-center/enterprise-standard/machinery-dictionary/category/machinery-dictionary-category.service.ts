import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { v7 as uuidv7 } from 'uuid';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus, MachineryDictionaryCategory } from '@/prisma/generated';
import { MachineryDictionaryCategoryWhereInput } from '@/prisma/generated/models';

import {
  MachineryDictionaryCategoryCreateDto,
  MachineryDictionaryCategoryResultDto,
  MachineryDictionaryCategoryUpdateDto
} from './machinery-dictionary-category.dto';

@Injectable()
export class MachineryDictionaryCategoryService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    versionId: string
  ): Promise<MachineryDictionaryCategoryResultDto[]> {
    const { tenantId, orgId } = reqUser;

    const list = await this.prisma.machineryDictionaryCategory.findMany({
      select: {
        machineryDictionaryVersionId: true,
        id: true,
        code: true,
        name: true,
        // type: true,
        remark: true,
        isActive: true,
        parentId: true,
        isLeaf: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryVersionId: versionId,
        isDeleted: false
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });

    return list;
  }

  async createOne(
    data: MachineryDictionaryCategoryCreateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.machineryDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({ tenantId, orgId, data });

    const newRecord = {
      ...data,
      tenantId,
      orgId,
      id: uuidv7(),
      createBy: userId,
      updateBy: userId
    } as MachineryDictionaryCategory;

    await this.processTreeInfo(tenantId, orgId, newRecord);

    await this.prisma.machineryDictionaryCategory.create({
      data: {
        ...newRecord,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.machineryDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.machineryDictionaryCategory.findUnique({
      select: {
        machineryDictionaryVersionId: true,
        parentId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: MachineryDictionaryCategoryUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.machineryDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({
      tenantId,
      orgId,
      data,
      id,
      machineryDictionaryVersionId: category.machineryDictionaryVersionId
    });

    await this.prisma.$transaction(async (tx) => {
      await tx.machineryDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        },
        data: {
          ...data,
          updateBy: userId
        }
      });

      if (data.isActive != null) {
        const childCategoryList =
          await tx.machineryDictionaryCategory.updateManyAndReturn({
            select: {
              id: true
            },
            where: {
              tenantId,
              orgId,
              id: { not: id },
              fullId: { contains: id },
              isDeleted: false
            },
            data: {
              isActive: data.isActive,
              updateBy: userId
            }
          });
        const categoryIds = [id, ...childCategoryList.map((i) => i.id)];
        await tx.machineryDictionaryDetail.updateMany({
          where: {
            tenantId,
            orgId,
            machineryDictionaryCategoryId: { in: categoryIds },
            isDeleted: false
          },
          data: {
            isActive: data.isActive,
            updateBy: userId
          }
        });
      }
    });
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.machineryDictionaryVersionId);

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    // 检查分类是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('分类被引用，无法删除！');
    }

    const ids = [id];

    // 如果有子级，则级联删除
    const children = await this.prisma.machineryDictionaryCategory.findMany({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        id: { not: id },
        fullId: { contains: id }
      }
    });
    ids.push(...children.map((item) => item.id));

    await this.prisma.$transaction(async (tx) => {
      await tx.machineryDictionaryCategory.updateMany({
        where: {
          tenantId,
          orgId,
          id: { in: ids }
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      // 删除分类后查询是否有父级分类，有则改变父级分类状态为非叶子节点
      await this.updateParentLeaf(
        tx as PrismaService,
        category.parentId || null,
        orgId,
        tenantId,
        reqUser.id
      );
    });
  }

  async updateParentLeaf(
    tx: PrismaService,
    parentId: string | null,
    orgId: string,
    tenantId: string,
    userId: string
  ) {
    if (parentId) {
      // 查询父级
      const parentCategory = await tx.machineryDictionaryCategory.findFirst({
        where: {
          id: parentId,
          isDeleted: false,
          orgId,
          tenantId
        },
        select: {
          id: true,
          children: {
            select: {
              id: true
            },
            where: {
              isDeleted: false
            }
          }
        }
      });
      if (!parentCategory?.children.length) {
        await tx.machineryDictionaryCategory.update({
          where: {
            id: parentId
          },
          data: {
            isLeaf: true,
            updateBy: userId,
            updateAt: new Date()
          }
        });
      }
    }
  }

  async moveOne(reqUser: IReqUser, id: string, moveTo: MoveToEnum) {
    const { tenantId, orgId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.machineryDictionaryVersionId);

    const currentRecord =
      await this.prisma.machineryDictionaryCategory.findFirst({
        select: {
          id: true,
          machineryDictionaryVersionId: true,
          parentId: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 处理树形信息
   */
  private async processTreeInfo(
    tenantId: string,
    orgId: string,
    data: MachineryDictionaryCategory
  ) {
    // 查询同级节点的最后一个数据，用于给当前数据生成排序号
    const findSiblingNodeLastWhereInput: MachineryDictionaryCategoryWhereInput =
      {
        tenantId,
        orgId,
        machineryDictionaryVersionId: data.machineryDictionaryVersionId,
        isDeleted: false
      };
    if (data.parentId) {
      findSiblingNodeLastWhereInput.parentId = data.parentId;
    }
    const maxSort = await this.prisma.machineryDictionaryCategory.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryVersionId: data.machineryDictionaryVersionId,
        parentId: data.parentId,
        isDeleted: false
      }
    });
    data.sort = (maxSort._max.sort || 0) + 1;

    if (data.parentId) {
      const parent = await this.prisma.machineryDictionaryCategory.findFirst({
        select: {
          fullId: true,
          fullName: true,
          level: true
        },
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId: data.machineryDictionaryVersionId,
          id: data.parentId,
          isDeleted: false
        }
      });
      if (!parent) {
        throw new BadRequestException('父级不存在');
      }

      data.fullId = `${parent.fullId}/${data.id}`;
      data.fullName = `${parent.fullName}/${data.name}`;
      data.level = parent.level + 1;
    } else {
      data.fullId = data.id;
      data.fullName = data.name;
    }

    // 默认初始都为叶子节点,存在父级数据后修改
    data.isLeaf = true;
    if (data.parentId) {
      await this.prisma.machineryDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId: data.machineryDictionaryVersionId,
          id: data.parentId
        },
        data: {
          isLeaf: false
        }
      });
    }
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data:
      | MachineryDictionaryCategoryCreateDto
      | MachineryDictionaryCategoryUpdateDto;
    id?: string;
    machineryDictionaryVersionId?: string;
  }) {
    const { tenantId, orgId, data, id, machineryDictionaryVersionId } = args;

    const code = data.code;
    const name = data.name;
    const duplicateCodeRecod =
      await this.prisma.machineryDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId: data.machineryDictionaryVersionId
            ? data.machineryDictionaryVersionId
            : machineryDictionaryVersionId,
          id: { not: id },
          code,
          isDeleted: false
        }
      });
    const duplicateNameRecod =
      await this.prisma.machineryDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId: data.machineryDictionaryVersionId
            ? data.machineryDictionaryVersionId
            : machineryDictionaryVersionId,
          id: { not: id },
          name,
          isDeleted: false
        }
      });
    if (duplicateCodeRecod) {
      throw new BadRequestException('编码重复，请重新输入！');
    }
    if (duplicateNameRecod) {
      throw new BadRequestException('名称重复，请重新输入！');
    }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    const childLevel = await this.prisma.machineryDictionaryCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        parentId: id,
        isDeleted: false
      }
    });
    if (childLevel) {
      throw new BadRequestException('分类下存在下级分类，不可删除！');
    }
    const detail = await this.prisma.machineryDictionaryDetail.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryCategoryId: id,
        isDeleted: false
      }
    });

    if (detail) {
      throw new BadRequestException('分类下存在明细，不可删除！');
    }
  }

  /**
   * 向上移动
   */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      machineryDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { machineryDictionaryVersionId } = currentRecord;

    const prevRecordWhere: MachineryDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      machineryDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        lt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      prevRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到上一条数据
    const prevRecord = await this.prisma.machineryDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: prevRecordWhere,
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.machineryDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.machineryDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId,
          id: prevRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /**
   * 向下移动
   */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      machineryDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { machineryDictionaryVersionId } = currentRecord;

    const nextRecordWhere: MachineryDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      machineryDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        gt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      nextRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到下一条数据
    const nextRecord = await this.prisma.machineryDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: nextRecordWhere,
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.machineryDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.machineryDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          machineryDictionaryVersionId,
          id: nextRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 获取分类项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountMechineryDictionaryCategory.count({
        where: {
          tenantId,
          isDeleted: false,
          categoryId: id
        }
      });
    return { projectRefCount };
  }
}
