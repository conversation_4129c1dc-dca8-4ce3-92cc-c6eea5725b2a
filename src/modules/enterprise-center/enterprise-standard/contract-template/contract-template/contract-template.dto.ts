import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateTemplateDto {
  @ApiProperty({ description: '范本名称', default: 'xxxx' })
  @IsNotEmpty({ message: '范本名称不能为空' })
  @IsString({ message: '范本名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '合同范本分类', default: 'xxxx' })
  @IsNotEmpty({ message: '合同范本分类不能为空' })
  @IsString({ message: '合同范本分类必须是字符串' })
  classify: ContractTemplateClassifyType;

  @ApiProperty({ description: '范本说明', default: 'xxxx' })
  @IsOptional()
  @IsString({ message: '范本说明必须是字符串' })
  remark?: string;

  @ApiProperty({ description: '文件类型', default: 'xxxx' })
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsString({ message: '文件类型必须是字符串' })
  fileContentType: string;

  @ApiProperty({ description: '文件名', default: 'xxxx' })
  @IsNotEmpty({ message: '文件名不能为空' })
  @IsString({ message: '文件名必须是字符串' })
  fileName: string;

  @ApiProperty({ description: '文件大小', default: 'xxxx' })
  @IsNotEmpty({ message: '文件大小不能为空' })
  @IsString({ message: '文件大小必须是字符串' })
  fileSize: string;

  @ApiProperty({ description: '文件key', default: 'xxxx' })
  @IsNotEmpty({ message: '文件key不能为空' })
  @IsString({ message: '文件key必须是字符串' })
  fileKey: string;
}

export class UpdateTemplateDto {
  @ApiProperty({ description: '范本名称', default: 'xxxx' })
  @IsNotEmpty({ message: '范本名称不能为空' })
  @IsString({ message: '范本名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '范本说明', default: 'xxxx' })
  @IsOptional()
  @IsString({ message: '范本说明必须是字符串' })
  remark?: string;

  @ApiProperty({ description: '文件id', default: 'xxxx' })
  @IsOptional()
  @IsString({ message: '文件id必须是字符串' })
  fileContentType?: string;

  @ApiProperty({ description: '文件名', default: 'xxxx' })
  @IsOptional()
  @IsString({ message: '文件名必须是字符串' })
  fileName?: string;

  @ApiProperty({ description: '文件大小', default: 'xxxx' })
  @IsOptional()
  @IsString({ message: '文件大小必须是字符串' })
  fileSize?: string;

  @ApiProperty({ description: '文件key', default: 'xxxx' })
  @IsOptional()
  @IsString({ message: '文件key必须是字符串' })
  fileKey?: string;
}

export class EditStatusDto {
  @ApiProperty({ description: 'id', default: 'xxxx' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '版本状态', default: 'xxxx' })
  @IsNotEmpty({ message: '版本状态不能为空' })
  @IsString({ message: '版本状态必须是字符串' })
  versionStatus: VersionStatus;
}

export class EditMoveDto {
  @ApiProperty({ description: 'id', default: 'xxxx' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '移动类型', default: 'up:上移，down：下移' })
  @IsNotEmpty({ message: '移动类型不能为空' })
  @IsString({ message: '移动类型必须是字符串' })
  moveType: 'up' | 'down';
}

enum VersionStatus {
  PUBLISHED = 'PUBLISHED', //  发布
  UNPUBLISHED = 'UNPUBLISHED', // 未发布
  NOUSEING = 'NOUSEING' // 停用
}

enum ContractTemplateClassifyType {
  SUBPACKAGE_LABOUR_SERVICE = 'SUBPACKAGE_LABOUR_SERVICE', // 分包-劳务
  SUBPACKAGE_LABOUR_SPECIALTY = 'SUBPACKAGE_LABOUR_SPECIALTY', // 分包-专业
  MATERIALS_PURCHASING = 'MATERIALS_PURCHASING', // 材料-物资采购
  MATERIALS_COMMERCIAL_CONCRETE = 'MATERIALS_COMMERCIAL_CONCRETE', // 材料-商品混凝土
  MATERIALS_LEASING_TURNOVER = 'MATERIALS_LEASING_TURNOVER', // 材料-租赁周转材料
  MACHINERY = 'MACHINERY', // 机械
  OTHERS = 'OTHERS' // 其他
}
