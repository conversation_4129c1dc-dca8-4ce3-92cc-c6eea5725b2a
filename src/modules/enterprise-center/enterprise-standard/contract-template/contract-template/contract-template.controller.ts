import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import {
  ContractTemplateClassifyType,
  VersionStatus
} from '@/prisma/generated';

import {
  CreateTemplateDto,
  EditMoveDto,
  EditStatusDto,
  UpdateTemplateDto
} from './contract-template.dto';
import { ContractTemplateService } from './contract-template.service';

@ApiTags('企业标准合同模板')
@Controller('enterprise-standard-contract-template')
export class ContractTemplateController {
  constructor(private readonly service: ContractTemplateService) {}

  @ApiOperation({
    summary: '新增合同模板',
    description: '新增合同模板'
  })
  @ApiResponse({ status: 200, description: '新增合同模板成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/add')
  async create(@ReqUser() reqUser: IReqUser, @Body() data: CreateTemplateDto) {
    // 判断合同模板类型是否存在
    const classifyType = new Set(Object.values(ContractTemplateClassifyType));
    if (!classifyType.has(data.classify as ContractTemplateClassifyType)) {
      throw new BadRequestException('合同范本分类不存在');
    }
    // 效验合同范本的名称
    // 若存在则加（数字）递归查询直至不存在
    data.name = await this.service.getNameNumber(data.name, reqUser);
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '获取分类列表',
    description: '获取分类列表'
  })
  @ApiResponse({ status: 200, description: '获取分类列表成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async getList(@ReqUser() reqUser: IReqUser) {
    return await this.service.getList(reqUser);
  }

  @ApiOperation({
    summary: '获取分类对象',
    description: '获取分类对象'
  })
  @ApiResponse({ status: 200, description: '获取分类对象成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/obj/:id')
  async getObj(@Param('id') id: string) {
    return await this.service.getObj(id);
  }

  @ApiOperation({
    summary: '编辑合同模板',
    description: '编辑合同模板'
  })
  @ApiResponse({ status: 200, description: '编辑合同模板成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/edit/:id')
  async edit(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateTemplateDto
  ) {
    // 效验合同范本的名称
    const template = await this.service.getName(
      data.name,
      reqUser.orgId,
      reqUser,
      'edit',
      id
    );
    if (template) {
      throw new HttpException('合同模板名称已存在', HttpStatus.BAD_REQUEST);
    }
    return await this.service.edit(id, data, reqUser);
  }

  @ApiOperation({
    summary: '修改状态',
    description: '修改状态'
  })
  @ApiResponse({ status: 200, description: '修改状态成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/edit/status')
  async editStatus(@ReqUser() reqUser: IReqUser, @Body() data: EditStatusDto) {
    // 查询该模版
    const template = await this.service.getObj(data.id);
    if (!template) {
      throw new HttpException('合同模板不存在', HttpStatus.BAD_REQUEST);
    }
    // 判断合同范本表版本状态是否存在
    const classifyType = new Set(Object.values(VersionStatus));
    if (!classifyType.has(data.versionStatus as VersionStatus)) {
      throw new BadRequestException('合同范本表版本状态不存在');
    }
    return await this.service.editStatus(data, reqUser.id, template);
  }

  @ApiOperation({
    summary: '修改上移下移',
    description: '修改上移下移'
  })
  @ApiResponse({ status: 200, description: '修改上移下移成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/edit/move')
  async editMove(@ReqUser() reqUser: IReqUser, @Body() data: EditMoveDto) {
    const { id, moveType } = data;
    if (moveType === 'up') {
      // 上移
      await this.service.up(id, reqUser);
    } else {
      // 下移
      await this.service.down(id, reqUser);
    }
    return true;
  }

  @ApiOperation({
    summary: '删除',
    description: '删除'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/delete/:id')
  async del(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.del(id, reqUser.id);
  }
}
