import { HttpException, HttpStatus, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ContractTemplateClassifyType } from '@/prisma/generated';

import {
  CreateFieldRuleDto,
  DelFieldRuleDto,
  QueryFieldRuleDto
} from './field-rule.dto';

@Injectable()
export class FieldRuleService {
  constructor(private readonly prisma: PrismaService) {}

  async hasData(contractTemplateId: string) {
    return await this.prisma.contractTemplateFieldRule.findFirst({
      where: {
        contractTemplateId,
        isDeleted: false
      }
    });
  }

  async getAllList(query: QueryFieldRuleDto, reqUser: IReqUser) {
    const searchTerm = `%${query.name}%`;
    return await this.prisma.$queryRaw`
      with tmp_classify as (
					select 
              '1' as id, '项目信息' as name, 1 as sort, null as parent_id, 1 as level,
              'PROJECT_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id
              
            union all
            
            select 
              '2' as id, '项目信息' as name, 2 as sort, null as parent_id, 1 as level,
              'Party_A_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id

            union all
            
            select 
						 '3' as id, '项目信息' as name, 3 as sort, null as parent_id, 1 as level,
							'Party_B_INFORMATION' as classify, false as is_required, null as field_type,
							null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id
              
            union all
            
            select 
              '4' as id, '项目信息' as name, 4 as sort, null as parent_id, 1 as level,
              'CONTRACT_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id
							
          ), tmp_field_rule as (
          SELECT
              esct.id,
              esct.name,
              esct.sort,
              tc.id AS parent_id, 
              2 as level,
              esct.classify::TEXT AS classify,
              esct.is_required,
							esct.field_type::TEXT AS field_type,
							esct.enum_value,
              esct.type::TEXT AS type,
              esct.module_source::TEXT AS module_source,
              esct.is_matching,
							esct.tenant_id
            FROM field_rule esct
            JOIN tmp_classify tc on esct.classify::TEXT = tc.classify
            WHERE
              esct.is_deleted = false
							AND (esct.tenant_id IS null OR esct.tenant_id = ${reqUser.tenantId})
							AND esct.name like ${searchTerm}
            order by esct.sort, esct.create_at DESC
          ) 
          select
            *
          from (
            select * from tmp_classify

            union all

            select * from tmp_field_rule
          ) as t
					WHERE (t.level = 1 AND EXISTS (SELECT 1 FROM tmp_field_rule WHERE parent_id = t.id))
						 OR t.level = 2
					ORDER BY level, sort, name
    `;
  }

  async getcurrentList(query: QueryFieldRuleDto, reqUser: IReqUser) {
    const searchTerm = `%${query.name}%`;
    // 查询模版类型
    const contractTemplate = await this.prisma.contractTemplate.findFirst({
      where: {
        id: query.contractTemplateId,
        isDeleted: false
      }
    });
    const searchFieldRuleType = [
      'GENERAL' as ContractTemplateClassifyType,
      contractTemplate?.classify as ContractTemplateClassifyType
    ];
    return await this.prisma.$queryRaw`
      with tmp_classify as (
					select 
              '1' as id, '项目信息' as name, 1 as sort, null as parent_id, 1 as level,
              'PROJECT_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id, null as coord, true AS is_exit, false as is_default_required,
              false as is_update, null as code
              
            union all
            
            select 
              '2' as id, '甲方信息' as name, 2 as sort, null as parent_id, 1 as level,
              'Party_A_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id, null as coord, true AS is_exit, false as is_default_required,
              false as is_update, null as code

            union all
            
            select 
						 '3' as id, '乙方信息' as name, 3 as sort, null as parent_id, 1 as level,
							'Party_B_INFORMATION' as classify, false as is_required, null as field_type,
							null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id, null as coord, true AS is_exit, false as is_default_required,
              false as is_update, null as code
              
            union all
            
            select 
              '4' as id, '合同信息' as name, 4 as sort, null as parent_id, 1 as level,
              'CONTRACT_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value, null as type, null as module_source, false as is_matching,
							null as tenant_id, null as coord, true AS is_exit, false as is_default_required,
              false as is_update, null as code
							
          ), tmp_field_rule as (
          SELECT
              esct.id,
              esct.name,
              esct.sort,
              tc.id AS parent_id, 
              2 as level,
              esct.classify::TEXT AS classify,
              COALESCE(ctfr.is_required, esct.is_required) AS is_required,
							esct.field_type::TEXT AS field_type,
							esct.enum_value,
              esct.type::TEXT AS type,
              esct.module_source::TEXT AS module_source,
              esct.is_matching,
							esct.tenant_id,
							ctfr.coord,
              (ctfr.field_rule_id IS NOT NULL and ctfr.is_deleted = false) AS is_exit,
              esct.is_default_required,
              esct.is_update,
              esct.code
            FROM field_rule esct
            JOIN tmp_classify tc on esct.classify::TEXT = tc.classify
						left join contract_template_field_rule ctfr 
							on ctfr.contract_template_id = ${query.contractTemplateId}
							and ctfr.field_rule_id = esct.id
							and ctfr.is_deleted = false
            WHERE
              esct.is_deleted = false
							AND (esct.tenant_id IS null OR esct.tenant_id = ${reqUser.tenantId})
							AND esct.name like ${searchTerm}
              AND esct.type::TEXT = ANY(${searchFieldRuleType}::TEXT[])
            order by esct.sort, esct.create_at DESC
          ) 
          select
            *
          from (
            select * from tmp_classify

            union all

            select * from tmp_field_rule
          ) as t
					WHERE (t.level = 1 AND EXISTS (SELECT 1 FROM tmp_field_rule WHERE parent_id = t.id))
						 OR t.level = 2
					ORDER BY level, sort, name
    `;
  }

  async add(data: CreateFieldRuleDto, reqUser: IReqUser) {
    const { contractTemplateId, fieldRuleId } = data;
    // 查询该条字段规则是否存在
    const fieldRule = await this.prisma.contractTemplateFieldRule.findFirst({
      where: {
        fieldRuleId,
        contractTemplateId,
        isDeleted: false
      }
    });
    if (fieldRule) {
      return await this.prisma.contractTemplateFieldRule.update({
        where: {
          id: fieldRule.id
        },
        data: {
          updateBy: reqUser.id,
          ...data
        }
      });
    } else {
      return await this.prisma.contractTemplateFieldRule.create({
        data: {
          ...data,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }
      });
    }
  }

  async del(data: DelFieldRuleDto, reqUser: IReqUser) {
    const { contractTemplateId, fieldRuleId, coord } = data;
    // 查询该条字段规则是否存在
    const fieldRule = await this.prisma.contractTemplateFieldRule.findFirst({
      where: {
        fieldRuleId,
        contractTemplateId,
        isDeleted: false
      }
    });
    if (fieldRule) {
      // 若存在，判断坐标
      const coords = fieldRule.coord.split(',');
      if (coords.length === 1) {
        // 若该字段规则只有一个坐标，则删除该条字段规则
        return await this.prisma.contractTemplateFieldRule.update({
          where: {
            id: fieldRule.id
          },
          data: {
            updateBy: reqUser.id,
            isDeleted: true
          }
        });
      } else {
        const newCoords = coords.filter((item) => {
          if (item !== coord) {
            return item;
          }
        });
        // 删除该坐标
        return await this.prisma.contractTemplateFieldRule.update({
          where: {
            id: fieldRule.id
          },
          data: {
            updateBy: reqUser.id,
            coord: newCoords.join(',')
          }
        });
      }
    }
  }
}
