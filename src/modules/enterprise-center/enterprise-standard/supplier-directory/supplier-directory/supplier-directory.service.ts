import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import { Utils } from 'src/public/utils';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { SupplierDirectorySeedService } from '@/common/modules/prisma/seed/services/supplier-directory.seed.service';
import { PlatformService } from '@/modules/platform/platform.service';

import { SupplierDirectoryAccessoryService } from '../supplier-directory-accessory/supplier-directory-accessory.service';
import {
  ChangeRecordDetailDto,
  ChangeType,
  DetailsFieldType
} from '../supplier-directory-change-record/supplier-directory-change-record.dto';
import { SupplierDirectoryChangeRecordService } from '../supplier-directory-change-record/supplier-directory-change-record.service';
import { CreateSupplierDto, UpdateSupplierDto } from './supplier-directory.dto';

@Injectable()
export class SupplierDirectoryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly supplierDirectoryAccessoryService: SupplierDirectoryAccessoryService,
    private readonly supplierDirectoryChangeRecordService: SupplierDirectoryChangeRecordService,
    private readonly supplierDirectorySeedService: SupplierDirectorySeedService,
    private readonly platformService: PlatformService
  ) {}

  private fieldMapping = {
    fullName: '供应商全称',
    simpleName: '供应商简称',
    creditCode: '统一社会信用代码',
    registeredProvince: '注册所在省',
    registeredCity: '注册所在市',
    registeredCounty: '注册所在区县',
    registeredAddress: '注册地址',
    mainBusiness: '主营业务',
    classify: '供应商分类',
    jobContent: '供应工作内容',
    region: '供应区域',
    unitType: '单位类型',
    taxpayerQualification: '纳税人资质',
    registeredCapital: '注册资金（万元）',
    establishAt: '成立时间',
    contactBy: '联系人',
    contactPhone: '联系人电话',
    contactEmail: '联系人邮箱',
    LegalBy: '法定代表人/单位负责人',
    relationEnterprise: '关联企业',
    introductionAt: '引进时间',
    remark: '备注'
    // grade: '供应商评级',
    // evaluateAt: '评价时间',
    // exitAt: '退场时间',
    // weedOutAt: '淘汰时间'
  };

  async add(data: CreateSupplierDto, reqUser: IReqUser) {
    let res;
    await this.prisma.$transaction(async (tx) => {
      const { accessoryList, ...rest } = data;
      // 新增供应商
      res = await tx.supplierDirectory.create({
        data: {
          ...rest,
          classify: data.classify,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          creator: reqUser.nickname,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }
      });
      // 新增附件
      await this.supplierDirectoryAccessoryService.add(
        tx as PrismaService,
        accessoryList,
        reqUser,
        res.id
      );
    });
    return res;
  }

  async getObjByCreditCode(creditCode: string, reqUser: IReqUser) {
    const res = await this.prisma.supplierDirectory.findFirst({
      where: {
        creditCode: creditCode,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
    return res;
  }

  async getObjByCreditCodeNotInOwer(
    id: string,
    creditCode: string,
    reqUser: IReqUser
  ) {
    const res = await this.prisma.supplierDirectory.findFirst({
      where: {
        creditCode: creditCode,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        id: {
          not: id
        }
      }
    });
    return res;
  }

  async edit(id: string, data: UpdateSupplierDto, reqUser: IReqUser) {
    const { accessoryList, ...rest } = data;
    let res;
    await this.prisma.$transaction(async (tx) => {
      res = await tx.supplierDirectory.update({
        where: {
          id
        },
        data: {
          ...rest,
          updateBy: reqUser.id
        }
      });
      // 获取附件需要新增的数据
      const addData = accessoryList.filter((item) => !item.id);
      // 获取附件需要删除的数据
      const delData = accessoryList.filter(
        (item) => item.id && item.type === true
      );
      // 新增附件
      await this.supplierDirectoryAccessoryService.add(
        tx as PrismaService,
        addData,
        reqUser,
        id
      );
      // 删除附件
      await this.supplierDirectoryAccessoryService.del(
        tx as PrismaService,
        delData,
        reqUser
      );
    });
    return res;
  }

  async publish(id: string, reqUser: IReqUser) {
    let res;
    await this.prisma.$transaction(async (tx) => {
      // 查询该供应商是否已发布
      const publishedRecord = await tx.publishSupplierDirectory.findUnique({
        select: {
          fullName: true,
          simpleName: true,
          creditCode: true,
          registeredProvince: true,
          registeredCity: true,
          registeredCounty: true,
          registeredAddress: true,
          mainBusiness: true,
          classify: true,
          jobContent: true,
          region: true,
          unitType: true,
          taxpayerQualification: true,
          registeredCapital: true,
          establishAt: true,
          contactBy: true,
          contactPhone: true,
          contactEmail: true,
          LegalBy: true,
          relationEnterprise: true,
          introductionAt: true,
          remark: true,
          supplierPubDirectoryAccessory: {
            select: {
              name: true,
              size: true,
              fileName: true,
              fileUrl: true,
              suffix: true,
              remark: true
            },
            where: {
              isDeleted: false
            }
          }
        },
        where: {
          id,
          isDeleted: false
        }
      });
      // 查询当前数据
      const currentRecord = await this.prisma.supplierDirectory.findUnique({
        select: {
          fullName: true,
          simpleName: true,
          creditCode: true,
          registeredProvince: true,
          registeredCity: true,
          registeredCounty: true,
          registeredAddress: true,
          mainBusiness: true,
          classify: true,
          jobContent: true,
          region: true,
          unitType: true,
          taxpayerQualification: true,
          registeredCapital: true,
          establishAt: true,
          contactBy: true,
          contactPhone: true,
          contactEmail: true,
          LegalBy: true,
          relationEnterprise: true,
          introductionAt: true,
          remark: true,
          supplierDirectoryAccessory: {
            select: {
              name: true,
              size: true,
              fileName: true,
              fileUrl: true,
              suffix: true,
              remark: true
            },
            where: {
              isDeleted: false
            }
          }
        },
        where: {
          id,
          isDeleted: false
        }
      });

      if (publishedRecord) {
        // 已发布则对比两份数据
        // 数据进行变更对比
        const changeList: ChangeRecordDetailDto[] = Utils.detectChanges(
          publishedRecord,
          currentRecord
        );
        // 获取两份附件数据进行比对
        const accessoryObj = await this.getPublishedAccessoryObj(
          id,
          tx as PrismaService
        );

        // 获取附件变更
        const fieldMapping = this.fieldMapping;
        const reqData = [
          ...accessoryObj.addData.map((item) => ({
            fieldName: '附件',
            newValue: item.name + '.' + item.suffix,
            changeType: ChangeType.ADD,
            fieldType: DetailsFieldType.FILE
          })),
          ...accessoryObj.delData.map((item) => ({
            fieldName: '附件',
            newValue: item.name + '.' + item.suffix,
            changeType: ChangeType.DELETE,
            fieldType: DetailsFieldType.FILE
          })),
          ...changeList.map((item) => ({
            fieldName:
              this.fieldMapping[item.fieldName as keyof typeof fieldMapping] ||
              item.fieldName,
            newValue: item.newValue,
            oldValue: item.oldValue,
            fieldType: DetailsFieldType.GENERAL
          }))
        ];

        // 变更录入
        await this.supplierDirectoryChangeRecordService.add(
          tx as PrismaService,
          reqData,
          reqUser,
          id,
          changeList.length +
            accessoryObj.addData.length +
            accessoryObj.delData.length
        );
        // 修改发布状态并用原数据覆盖发布数据
        res = await this.changeDataPublish(
          currentRecord,
          accessoryObj,
          tx as PrismaService,
          reqUser,
          id
        );
      } else if (currentRecord) {
        // 未发布则新增发布数据
        res = await this.publishNewData(
          tx as PrismaService,
          reqUser,
          id,
          currentRecord
        );
      }
    });
    return res;
  }

  async unPublish(id: string, reqUser: IReqUser) {
    return await this.prisma.supplierDirectory.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        isPublished: false,
        updateBy: reqUser.id
      }
    });
  }

  // 变更发布修改数据
  async changeDataPublish(
    currentRecord: any,
    accessoryObj: any,
    tx: PrismaService,
    reqUser: IReqUser,
    id: string
  ) {
    const { supplierDirectoryAccessory, ...reset } = currentRecord;
    // 修改发布状态
    const res = await tx.supplierDirectory.update({
      where: {
        id
      },
      data: {
        publishAt: new Date(),
        isPublished: true,
        updateBy: reqUser.id
      }
    });
    // 发布数据修改
    await tx.publishSupplierDirectory.update({
      where: {
        id
      },
      data: {
        ...reset,
        updateBy: reqUser.id
      }
    });
    // 新增附件
    await this.supplierDirectoryAccessoryService.addPublish(
      tx,
      accessoryObj.addData,
      reqUser,
      id
    );
    // // 删除附件
    await this.supplierDirectoryAccessoryService.delPublish(
      tx,
      accessoryObj.delData,
      reqUser
    );
    return res;
  }

  // 发布新数据
  async publishNewData(
    tx: PrismaService,
    reqUser: IReqUser,
    id: string,
    currentRecord: any
  ) {
    const { supplierDirectoryAccessory, ...reset } = currentRecord;
    await tx.publishSupplierDirectory.create({
      data: {
        id: id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        ...reset,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    if (currentRecord.supplierDirectoryAccessory.length) {
      await tx.supplierPublishDirectoryAccessory.createMany({
        data: currentRecord.supplierDirectoryAccessory.map((item: any) => ({
          supplierPubDirectoryId: id,
          ...item,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }))
      });
    }
    return await tx.supplierDirectory.update({
      where: {
        id: id
      },
      data: {
        publishAt: new Date(),
        isPublished: true,
        updateBy: reqUser.id
      }
    });
  }

  // 获取附件变更数据
  async getPublishedAccessoryObj(id: string, prisma: PrismaService) {
    const publishedAccessoryList =
      await prisma.supplierPublishDirectoryAccessory.findMany({
        select: {
          id: true,
          name: true,
          size: true,
          suffix: true,
          fileName: true,
          fileUrl: true
        },
        where: {
          supplierPubDirectoryId: id,
          isDeleted: false
        }
      });
    const currentAccessoryList =
      await prisma.supplierDirectoryAccessory.findMany({
        select: {
          id: true,
          name: true,
          size: true,
          suffix: true,
          fileName: true,
          fileUrl: true
        },
        where: {
          supplierDirectoryId: id,
          isDeleted: false
        }
      });

    // 附件比对
    // 创建 url 到对象的映射
    const publishedMap = new Map(
      publishedAccessoryList.map((item) => [item.fileName, item])
    );
    const currentMap = new Map(
      currentAccessoryList.map((item) => [item.fileName, item])
    );

    // 找出删除数据
    const delData = publishedAccessoryList.filter(
      (item) => !currentMap.has(item.fileName)
    );

    // 找出新增数据
    const addData = currentAccessoryList.filter(
      (item) => !publishedMap.has(item.fileName)
    );
    return {
      addData,
      delData
    };
  }

  async del(id: string, reqUser: IReqUser) {
    // 此处缺少被引用查询校验，被引用后不可删除
    return await this.prisma.supplierDirectory.update({
      where: {
        id
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  async getList(req: Request, reqUser: IReqUser) {
    // 查询当前组织是否为跟组织
    const topOrg = (
      await this.platformService.getOrgs(req, reqUser.tenantId, [reqUser.orgId])
    )[0];
    if (topOrg.parentId === null) {
      await this.supplierDirectorySeedService.init(this.prisma, reqUser);
    }
    const data = await this.prisma.supplierDirectory.findMany({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      orderBy: [
        {
          isDefault: 'desc'
        },
        {
          createAt: 'desc'
        }
      ]
    });
    return data.map((item) => ({
      ...item,
      establishAt: item.establishAt
        ? dayjs(item.establishAt).format('YYYY-MM-DD')
        : item.establishAt,
      introductionAt: item.introductionAt
        ? dayjs(item.introductionAt).format('YYYY-MM-DD HH:mm:ss')
        : item.introductionAt,
      publishAt: item.publishAt
        ? dayjs(item.publishAt).format('YYYY-MM-DD HH:mm:ss')
        : item.publishAt
      // evaluateAt: dayjs(item.evaluateAt).format('YYYY-MM-DD HH:mm:ss'),
      // exitAt: dayjs(item.exitAt).format('YYYY-MM-DD HH:mm:ss'),
      // weedOutAt: dayjs(item.weedOutAt).format('YYYY-MM-DD HH:mm:ss')
    }));
  }

  async getObj(id: string) {
    const data = await this.prisma.supplierDirectory.findUnique({
      include: {
        supplierDirectoryAccessory: true,
        supplierDirectoryChangeRecord: true
      },
      where: {
        id,
        isDeleted: false
      }
    });

    return {
      ...data,
      establishAt: data?.establishAt
        ? dayjs(data?.establishAt).format('YYYY-MM-DD HH:mm:ss')
        : data?.establishAt,
      introductionAt: data?.introductionAt
        ? dayjs(data?.introductionAt).format('YYYY-MM-DD HH:mm:ss')
        : data?.introductionAt,
      publishAt: data?.publishAt
        ? dayjs(data?.publishAt).format('YYYY-MM-DD HH:mm:ss')
        : data?.publishAt
      // evaluateAt: dayjs(data?.evaluateAt).format('YYYY-MM-DD HH:mm:ss'),
      // exitAt: dayjs(data?.exitAt).format('YYYY-MM-DD HH:mm:ss'),
      // weedOutAt: dayjs(data?.weedOutAt).format('YYYY-MM-DD HH:mm:ss')
    };
  }
}
