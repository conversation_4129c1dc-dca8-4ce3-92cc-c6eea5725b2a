import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateAccessoryListDto {
  @ApiProperty({
    description: 'id,若不存在id的则为新增，若存在id则判断状态',
    default: 'xxxxxxxxxxxx'
  })
  @IsOptional()
  @IsString({ message: 'id必须为字符串' })
  id?: string;

  @ApiProperty({
    description: '状态：true则为删除，false的则不进行处理',
    default: 'true'
  })
  @IsOptional()
  @IsBoolean({ message: 'i状态必须为boolean' })
  type?: boolean;

  @ApiProperty({ description: '附件名称', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '附件名称不能为空' })
  @IsString({ message: '附件名称必须为字符串' })
  name: string;

  @ApiProperty({ description: '附件key', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '附件key不能为空' })
  @IsString({ message: '附件key必须为字符串' })
  fileName: string;

  @ApiProperty({ description: '附件地址', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '附件地址不能为空' })
  @IsString({ message: '附件地址必须为字符串' })
  fileUrl: string;

  @ApiProperty({ description: '附件后缀', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '附件后缀不能为空' })
  @IsString({ message: '附件后缀必须为字符串' })
  suffix: string;

  @ApiProperty({ description: '附件大小', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '附件大小不能为空' })
  @IsString({ message: '附件大小必须为字符串' })
  size: string;

  @ApiProperty({ description: '备注', default: 'xxxxxxxxxxxx' })
  @IsOptional()
  @IsString({ message: '备注必须为字符串' })
  remark?: string;
}

export class QueryDirectoryAccessoryDto {
  @ApiProperty({ description: '供应商id', default: 'xxxxxxxxxxxx' })
  @IsNotEmpty({ message: '供应商id不能为空' })
  @IsString({ message: '供应商id必须为字符串' })
  supplierDirectoryId: string;
}
