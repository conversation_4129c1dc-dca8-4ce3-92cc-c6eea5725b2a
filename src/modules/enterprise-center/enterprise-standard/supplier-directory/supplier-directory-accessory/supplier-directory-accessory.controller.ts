import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { QueryDirectoryAccessoryDto } from './supplier-directory-accessory.dto';
import { SupplierDirectoryAccessoryService } from './supplier-directory-accessory.service';

@ApiTags('供应商名录附件')
@Controller('supplier-directory-accessory')
export class SupplierDirectoryAccessoryController {
  constructor(private readonly service: SupplierDirectoryAccessoryService) {}

  @ApiOperation({
    summary: '查询供应商附件',
    description: '查询供应商附件'
  })
  @ApiResponse({ status: 200, description: '查询供应商附件成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async list(@Query() query: QueryDirectoryAccessoryDto) {
    return await this.service.getList(query);
  }
}
