import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { FileManageService } from '@/modules/file-manage/file-manage.service';

import {
  CreateAccessoryListDto,
  QueryDirectoryAccessoryDto
} from './supplier-directory-accessory.dto';

@Injectable()
export class SupplierDirectoryAccessoryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileManageService: FileManageService
  ) {}

  async getList(query: QueryDirectoryAccessoryDto) {
    return await this.prisma.supplierDirectoryAccessory.findMany({
      where: {
        isDeleted: false,
        supplierDirectoryId: query.supplierDirectoryId
      },
      orderBy: {
        createAt: 'desc'
      }
    });
  }

  async add(
    tx: PrismaService,
    data: CreateAccessoryListDto[],
    reqUser: IReqUser,
    supplierDirectoryId: string
  ) {
    if (data.length) {
      const mappedData = await Promise.all(
        data.map(async (item) => ({
          ...item,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          name: await this.getNameNumber(item.name, supplierDirectoryId),
          createBy: reqUser.id,
          updateBy: reqUser.id,
          supplierDirectoryId
        }))
      );
      await tx.supplierDirectoryAccessory.createMany({
        data: mappedData
      });
    }
  }

  async addPublish(
    tx: PrismaService,
    data: CreateAccessoryListDto[],
    reqUser: IReqUser,
    supplierPubDirectoryId: string
  ) {
    if (data.length) {
      const mappedData = await Promise.all(
        data.map((item) => ({
          ...item,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          createBy: reqUser.id,
          updateBy: reqUser.id,
          supplierPubDirectoryId
        }))
      );
      await tx.supplierPublishDirectoryAccessory.createMany({
        data: mappedData
      });
    }
  }

  async del(
    tx: PrismaService,
    data: CreateAccessoryListDto[],
    reqUser: IReqUser
  ) {
    for (const element of data) {
      await this.fileManageService.delete(element.fileName);
      await tx.supplierDirectoryAccessory.update({
        where: { id: element.id },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    }
  }

  async delPublish(
    tx: PrismaService,
    data: CreateAccessoryListDto[],
    reqUser: IReqUser
  ) {
    for (const element of data) {
      await tx.supplierPublishDirectoryAccessory.update({
        where: { id: element.id },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    }
  }

  // 文件名存在则加（数字）
  async getNameNumber(
    name: string,
    supplierDirectoryId: string
  ): Promise<string> {
    const { baseName } = this.parseFileName(name);

    // 查询数据库中所有匹配基础名称的文件
    const existingFiles = await this.prisma.supplierDirectoryAccessory.findMany(
      {
        where: {
          name: {
            startsWith: baseName
          },
          supplierDirectoryId,
          isDeleted: false
        },
        select: {
          name: true
        }
      }
    );
    // 生成唯一名称
    const uniqueName = this.generateUniqueName(existingFiles, name);

    return uniqueName;
  }

  // 解析文件名，提取基础名称和后缀编号
  parseFileName(name: string) {
    const regex = /^(.*?)\s*\((\d+)\)$/;
    const match = name.match(regex);

    if (match) {
      return {
        baseName: match[1],
        number: parseInt(match[2], 10)
      };
    }

    return {
      baseName: name,
      number: null
    };
  }

  // 生成唯一名称
  generateUniqueName(existingNames: any[], originalName: string) {
    const { baseName, number } = this.parseFileName(originalName);

    // 提取所有已存在的编号
    const existingNumbers = existingNames
      .map((name) => {
        const match = name.name.match(/\((\d+)\)$/);
        return match ? parseInt(match[1], 10) : null;
      })
      .filter((n) => n !== null);

    // 如果原始名称没有编号，从 1 开始
    if (number === null) {
      const maxNumber = Math.max(0, ...existingNumbers);
      return existingNames.some((f) => f.name === originalName)
        ? `${baseName} (${maxNumber + 1})`
        : originalName;
    }

    // 如果原始名称有编号，找到不冲突的最小大于编号
    const nextNumber = this.findNextAvailableNumber(existingNumbers, number);
    return `${baseName}(${nextNumber})`;
  }

  // 找到大于等于 startNumber 的最小可用编号
  findNextAvailableNumber(existingNumbers: any[], startNumber: number) {
    let num = startNumber;
    while (existingNumbers.includes(num)) {
      num++;
    }
    return num;
  }
}
