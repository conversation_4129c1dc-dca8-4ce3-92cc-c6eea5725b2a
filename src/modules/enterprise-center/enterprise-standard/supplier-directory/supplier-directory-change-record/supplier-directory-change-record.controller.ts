import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { SupplierDirectoryChangeRecordService } from './supplier-directory-change-record.service';

@ApiTags('供应商变更记录')
@Controller('supplier-directory-change-record')
export class SupplierDirectoryChangeRecordController {
  constructor(private readonly service: SupplierDirectoryChangeRecordService) {}

  // @ApiOperation({
  //   summary: '查询变更记录',
  //   description: '查询变更记录'
  // })
  // @ApiResponse({ status: 200, description: '查询变更记录成功' })
  // @ApiResponse({ status: 500, description: '参数错误' })
  // @Get('/record-list')
  // async recordList(@Query() query: QueryChangeRecordDto) {
  //   return await this.service.getRecords(query.supplierDirectoryId);
  // }

  @ApiOperation({
    summary: '查询变更记录详情',
    description: '查询变更记录详情'
  })
  @ApiResponse({ status: 200, description: '查询变更记录详情成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/details-list/:supplierDirectoryId')
  async detailList(@Param('supplierDirectoryId') supplierDirectoryId: string) {
    return await this.service.getDetails(supplierDirectoryId);
  }
}
