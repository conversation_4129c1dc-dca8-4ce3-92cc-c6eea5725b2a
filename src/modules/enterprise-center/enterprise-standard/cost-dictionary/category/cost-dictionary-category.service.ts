import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { v7 as uuidv7 } from 'uuid';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { CostDictionaryCategory, EnableStatus } from '@/prisma/generated';
import { CostDictionaryCategoryWhereInput } from '@/prisma/generated/models';

import {
  CostDictionaryCategoryCreateDto,
  CostDictionaryCategoryResultDto,
  CostDictionaryCategoryUpdateDto
} from './cost-dictionary-category.dto';

@Injectable()
export class CostDictionaryCategoryService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    versionId: string
  ): Promise<CostDictionaryCategoryResultDto[]> {
    const { tenantId, orgId } = reqUser;

    const list = await this.prisma.costDictionaryCategory.findMany({
      select: {
        costDictionaryVersionId: true,
        id: true,
        code: true,
        name: true,
        // type: true,
        remark: true,
        isActive: true,
        parentId: true,
        isLeaf: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        costDictionaryVersionId: versionId,
        isDeleted: false
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });

    return list;
  }

  async createOne(data: CostDictionaryCategoryCreateDto, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.costDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({ tenantId, orgId, data });

    const newRecord = {
      ...data,
      tenantId,
      orgId,
      id: uuidv7(),
      createBy: userId,
      updateBy: userId
    } as CostDictionaryCategory;

    await this.processTreeInfo(tenantId, orgId, newRecord);

    await this.prisma.costDictionaryCategory.create({
      data: {
        ...newRecord,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.costDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.costDictionaryCategory.findUnique({
      select: {
        costDictionaryVersionId: true,
        parentId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: CostDictionaryCategoryUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.costDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({
      tenantId,
      orgId,
      data,
      id,
      costDictionaryVersionId: category.costDictionaryVersionId
    });

    await this.prisma.$transaction(async (tx) => {
      await tx.costDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        },
        data: {
          ...data,
          updateBy: userId
        }
      });
      if (data.isActive != null) {
        const childCategoryList =
          await tx.costDictionaryCategory.updateManyAndReturn({
            select: {
              id: true
            },
            where: {
              tenantId,
              orgId,
              id: { not: id },
              fullId: { contains: id },
              isDeleted: false
            },
            data: {
              isActive: data.isActive,
              updateBy: userId
            }
          });
        const categoryIds = [id, ...childCategoryList.map((i) => i.id)];
        await tx.costDictionaryDetail.updateMany({
          where: {
            tenantId,
            orgId,
            costDictionaryCategoryId: { in: categoryIds },
            isDeleted: false
          },
          data: {
            isActive: data.isActive,
            updateBy: userId
          }
        });
      }
    });
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.costDictionaryVersionId);

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    // 检查分类是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('分类被引用，无法删除！');
    }

    const ids = [id];

    // 如果有子级，则级联删除
    const children = await this.prisma.costDictionaryCategory.findMany({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        id: { not: id },
        fullId: { contains: id }
      }
    });
    ids.push(...children.map((item) => item.id));

    await this.prisma.$transaction(async (tx) => {
      await tx.costDictionaryCategory.updateMany({
        where: {
          tenantId,
          orgId,
          id: { in: ids }
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      // 删除分类后查询是否有父级分类，有则改变父级分类状态为非叶子节点
      await this.updateParentLeaf(
        tx as PrismaService,
        category.parentId || null,
        orgId,
        tenantId,
        reqUser.id
      );
    });
  }

  async updateParentLeaf(
    tx: PrismaService,
    parentId: string | null,
    orgId: string,
    tenantId: string,
    userId: string
  ) {
    if (parentId) {
      // 查询父级
      const parentCategory = await tx.costDictionaryCategory.findFirst({
        where: {
          id: parentId,
          isDeleted: false,
          orgId,
          tenantId
        },
        select: {
          id: true,
          children: {
            select: {
              id: true
            },
            where: {
              isDeleted: false
            }
          }
        }
      });
      if (!parentCategory?.children.length) {
        await tx.costDictionaryCategory.update({
          where: {
            id: parentId
          },
          data: {
            isLeaf: true,
            updateBy: userId,
            updateAt: new Date()
          }
        });
      }
    }
  }

  async moveOne(reqUser: IReqUser, id: string, moveTo: MoveToEnum) {
    const { tenantId, orgId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.costDictionaryVersionId);

    const currentRecord = await this.prisma.costDictionaryCategory.findFirst({
      select: {
        id: true,
        costDictionaryVersionId: true,
        parentId: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        id,
        isDeleted: false
      }
    });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 处理树形信息
   */
  private async processTreeInfo(
    tenantId: string,
    orgId: string,
    data: CostDictionaryCategory
  ) {
    // 查询同级节点的最后一个数据，用于给当前数据生成排序号
    const findSiblingNodeLastWhereInput: CostDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      costDictionaryVersionId: data.costDictionaryVersionId,
      isDeleted: false
    };
    if (data.parentId) {
      findSiblingNodeLastWhereInput.parentId = data.parentId;
    }
    const maxSort = await this.prisma.costDictionaryCategory.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        costDictionaryVersionId: data.costDictionaryVersionId,
        parentId: data.parentId,
        isDeleted: false
      }
    });
    data.sort = (maxSort._max.sort || 0) + 1;

    if (data.parentId) {
      const parent = await this.prisma.costDictionaryCategory.findFirst({
        select: {
          fullId: true,
          fullName: true,
          level: true
        },
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId: data.costDictionaryVersionId,
          id: data.parentId,
          isDeleted: false
        }
      });
      if (!parent) {
        throw new BadRequestException('父级不存在');
      }

      data.fullId = `${parent.fullId}/${data.id}`;
      data.fullName = `${parent.fullName}/${data.name}`;
      data.level = parent.level + 1;
    } else {
      data.fullId = data.id;
      data.fullName = data.name;
    }

    // 默认初始都为叶子节点,存在父级数据后修改
    data.isLeaf = true;
    if (data.parentId) {
      await this.prisma.costDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId: data.costDictionaryVersionId,
          id: data.parentId
        },
        data: {
          isLeaf: false
        }
      });
    }
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data: CostDictionaryCategoryCreateDto | CostDictionaryCategoryUpdateDto;
    id?: string;
    costDictionaryVersionId?: string;
  }) {
    const { tenantId, orgId, data, id, costDictionaryVersionId } = args;

    const code = data.code;
    const name = data.name;

    const duplicateCodeRecod =
      await this.prisma.costDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId: data.costDictionaryVersionId
            ? data.costDictionaryVersionId
            : costDictionaryVersionId,
          id: { not: id },
          code,
          isDeleted: false
        }
      });
    const duplicateNameRecod =
      await this.prisma.costDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId: data.costDictionaryVersionId
            ? data.costDictionaryVersionId
            : costDictionaryVersionId,
          id: { not: id },
          name,
          isDeleted: false
        }
      });
    if (duplicateCodeRecod) {
      throw new BadRequestException('编码重复，请重新输入！');
    }
    if (duplicateNameRecod) {
      throw new BadRequestException('名称重复，请重新输入！');
    }

    // const duplicateRecord = await this.prisma.costDictionaryCategory.findFirst({
    //   select: {
    //     id: true
    //   },
    //   where: {
    //     tenantId,
    //     orgId,
    //     costDictionaryVersionId: data.costDictionaryVersionId,
    //     id: { not: id },
    //     code,
    //     name,
    //     isDeleted: false
    //   }
    // });

    // if (duplicateRecord) {
    //   throw new BadRequestException('编码+名称重复，请重新输入！');
    // }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    const childLevel = await this.prisma.costDictionaryCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        parentId: id,
        isDeleted: false
      }
    });
    if (childLevel) {
      throw new BadRequestException('分类下存在下级分类，不可删除！');
    }
    const detail = await this.prisma.costDictionaryDetail.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        costDictionaryCategoryId: id,
        isDeleted: false
      }
    });

    if (detail) {
      throw new BadRequestException('分类下存在明细，不可删除！');
    }
  }

  /**
   * 向上移动
   */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      costDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { costDictionaryVersionId } = currentRecord;

    const prevRecordWhere: CostDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      costDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        lt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      prevRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到上一条数据
    const prevRecord = await this.prisma.costDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: prevRecordWhere,
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.costDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.costDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId,
          id: prevRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /**
   * 向下移动
   */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      costDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { costDictionaryVersionId } = currentRecord;

    const nextRecordWhere: CostDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      costDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        gt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      nextRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到下一条数据
    const nextRecord = await this.prisma.costDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: nextRecordWhere,
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.costDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.costDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          costDictionaryVersionId,
          id: nextRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 获取分类项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountCostDictionaryCategory.count({
        where: {
          tenantId,
          isDeleted: false,
          categoryId: id
        }
      });
    return { projectRefCount };
  }
}
