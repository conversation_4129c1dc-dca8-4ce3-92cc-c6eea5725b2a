import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import { ProjectBasicInfoCategoryService } from '../project-basic-info-category/project-basic-info-category.service';
import {
  CreateProjectBasicInfoFieldDetailDto,
  MoveProjectBasicDetailDto,
  SaveProjectBasicInfoFieldValueList,
  UpdateProjectBasicDetailsEnableDto,
  UpdateProjectBasicInfoFieldDetailDto
} from './project-basic-info-field-detail.dto';
import { ProjectBasicInfoFieldDetailService } from './project-basic-info-field-detail.service';

@ApiTags('项目基础信息-字段明细')
@Controller('project-basic-info-field-detail')
export class ProjectBasicInfoFieldDetailController {
  constructor(
    private readonly service: ProjectBasicInfoFieldDetailService,
    private readonly projectBasicInfoCategoryService: ProjectBasicInfoCategoryService
  ) {}

  @ApiOperation({
    summary: '新增字段明细',
    description: '新增字段明细'
  })
  @ApiResponse({ status: 200, description: '新增字段明细成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/add')
  async create(
    @ReqUser() reqUser: IReqUser,
    @Body() data: CreateProjectBasicInfoFieldDetailDto
  ) {
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '编辑字段明细',
    description: '编辑字段明细'
  })
  @ApiResponse({ status: 200, description: '编辑字段明细成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/edit/:id')
  async edit(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateProjectBasicInfoFieldDetailDto
  ) {
    return await this.service.edit(id, data, reqUser);
  }

  @ApiOperation({
    summary: '保存字段数据值',
    description: '保存字段数据值'
  })
  @ApiResponse({ status: 200, description: '保存字段数据值成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/saveValue')
  async saveValue(
    @ReqUser() reqUser: IReqUser,
    @Body() data: SaveProjectBasicInfoFieldValueList
  ) {
    return await this.service.saveValue(reqUser, data.data);
  }

  @ApiOperation({
    summary: '查询字段明细',
    description: '查询字段明细'
  })
  @ApiResponse({ status: 200, description: '查询字段明细成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list/:basicProjectInfoCategoryId')
  async list(
    @Param('basicProjectInfoCategoryId') basicProjectInfoCategoryId: string
  ) {
    return await this.service.getList(basicProjectInfoCategoryId);
  }

  @ApiOperation({
    summary: '删除字段明细',
    description: '删除字段明细'
  })
  @ApiResponse({ status: 200, description: '删除字段明细成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/del/:id')
  async del(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.del(id, reqUser);
  }

  @ApiOperation({
    summary: '查询字段明细对象',
    description: '查询字段明细对象'
  })
  @ApiResponse({ status: 200, description: '查询字段明细对象成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/obj/:id')
  async obj(@Param('id') id: string) {
    return await this.service.getObj(id);
  }

  @ApiOperation({
    summary: '修改上移下移',
    description: '修改上移下移'
  })
  @ApiResponse({ status: 200, description: '修改上移下移成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/edit/move')
  async editMove(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MoveProjectBasicDetailDto
  ) {
    const { id, moveType } = data;
    const detail = await this.service.getObj(id);
    if (detail?.basicProjectInfoCategory?.isPublish === true) {
      throw new HttpException(
        '已发布分组下的字段不可移动',
        HttpStatus.BAD_REQUEST
      );
    }
    if (moveType === 'up') {
      // 上移
      await this.service.up(id, reqUser);
    } else {
      // 下移
      await this.service.down(id, reqUser);
    }
    return true;
  }

  @ApiOperation({
    summary: '字段明细修改启用状态',
    description: '字段明细修改启用状态'
  })
  @ApiResponse({ status: 200, description: '字段明细修改启用状态成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/publish/:id')
  async publish(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateProjectBasicDetailsEnableDto
  ) {
    return await this.service.enable(id, reqUser, data);
  }

  @ApiOperation({
    summary: '查询项目信息',
    description: '查询项目信息'
  })
  @ApiResponse({ status: 200, description: '查询项目信息成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/allList')
  async getAllList(@Req() req: Request, @ReqUser() reqUser: IReqUser) {
    // 查询数据信息
    return await this.service.getListAll(req, reqUser);
  }
}
