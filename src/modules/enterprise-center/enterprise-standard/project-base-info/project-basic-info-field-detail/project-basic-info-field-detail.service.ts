import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { BasicProjectFieldType, EnableStatus } from '@/prisma/generated';

import { ProjectBasicInfoCategoryService } from '../project-basic-info-category/project-basic-info-category.service';
import {
  CreateProjectBasicInfoFieldDetailDto,
  SaveProjectBasicInfoFieldValue,
  UpdateProjectBasicDetailsEnableDto,
  UpdateProjectBasicInfoFieldDetailDto
} from './project-basic-info-field-detail.dto';

@Injectable()
export class ProjectBasicInfoFieldDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly projectBasicInfoCategoryService: ProjectBasicInfoCategoryService,
    private readonly platformService: PlatformService
  ) {}

  async getObjByName(
    reqUser: IReqUser,
    name: string,
    basicProjectInfoCategoryId: string
  ) {
    return await this.prisma.basicProjectInfoFieldDetail.findFirst({
      where: {
        name,
        basicProjectInfoCategoryId,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
  }

  async getObjByNameNotInOwer(id: string, reqUser: IReqUser, name: string) {
    return await this.prisma.basicProjectInfoFieldDetail.findFirst({
      where: {
        id: {
          not: id
        },
        name,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
  }

  async add(data: CreateProjectBasicInfoFieldDetailDto, reqUser: IReqUser) {
    // 新增校验
    await this.checkAdd(reqUser, data);
    // 查询最大的sort
    const maxSort = await this.getMaxSort(
      reqUser,
      data.basicProjectInfoCategoryId
    );
    return await this.prisma.basicProjectInfoFieldDetail.create({
      data: {
        ...data,
        type: data.type as BasicProjectFieldType,
        sort: maxSort,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
  }

  async checkAdd(
    reqUser: IReqUser,
    data: CreateProjectBasicInfoFieldDetailDto
  ) {
    const category = await this.projectBasicInfoCategoryService.getObj(
      data.basicProjectInfoCategoryId
    );
    if (category?.isPublish === true) {
      throw new HttpException(
        '已发布分组下的字段不可添加',
        HttpStatus.BAD_REQUEST
      );
    }
    // 校验字段名称
    const isExist = await this.getObjByName(
      reqUser,
      data.name,
      data.basicProjectInfoCategoryId
    );
    if (isExist) {
      throw new HttpException('字段名称已存在', HttpStatus.BAD_REQUEST);
    }
  }

  // 查询最大的排序
  async getMaxSort(reqUser: IReqUser, basicProjectInfoCategoryId: string) {
    const maxSort = await this.prisma.basicProjectInfoFieldDetail.aggregate({
      _max: {
        sort: true
      },
      where: {
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        basicProjectInfoCategoryId
      }
    });
    return maxSort?._max?.sort ? maxSort?._max?.sort + 1 : 1;
  }

  async edit(
    id: string,
    data: UpdateProjectBasicInfoFieldDetailDto,
    reqUser: IReqUser
  ) {
    // 编辑校验
    await this.checkEditConstraint(reqUser, id, data);
    return await this.prisma.basicProjectInfoFieldDetail.update({
      where: {
        id
      },
      data: {
        ...data,
        type: data.type as BasicProjectFieldType,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        updateBy: reqUser.id
      }
    });
  }

  async checkEditConstraint(
    reqUser: IReqUser,
    id: string,
    data: UpdateProjectBasicInfoFieldDetailDto
  ) {
    const detail = await this.getObj(id);
    const category = await this.projectBasicInfoCategoryService.getObj(
      data.basicProjectInfoCategoryId
    );
    if (category?.isPublish === true) {
      throw new HttpException(
        '已发布分组下的字段不可编辑',
        HttpStatus.BAD_REQUEST
      );
    }
    if (detail?.basicProjectInfoCategory?.isPublish === true) {
      throw new HttpException(
        '已发布分组下的字段不可编辑',
        HttpStatus.BAD_REQUEST
      );
    }
    // 校验名称不包含自己
    const isExist = await this.getObjByNameNotInOwer(id, reqUser, data.name);
    if (isExist) {
      throw new HttpException('名称已存在', HttpStatus.BAD_REQUEST);
    }
  }

  async saveValue(reqUser: IReqUser, data: SaveProjectBasicInfoFieldValue[]) {
    const { orgId, tenantId, id: userId } = reqUser;
    // 查询该项目基本信息是否已初始化
    const projectBasicInfo = await this.prisma.basicProjectInfoLedger.findMany({
      where: {
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
    await this.prisma.$transaction(async (tx) => {
      await Promise.all(
        data.map(async (item) => {
          const isExist = projectBasicInfo.find(
            (record) =>
              record.basicProjectInfoFieldDetailId === item.id &&
              record.orgId === orgId
          );
          if (!isExist) {
            await this.prisma.basicProjectInfoLedger.create({
              data: {
                tenantId,
                basicProjectInfoFieldDetailId: item.id,
                orgId: orgId,
                value: item.value,
                updateBy: userId
              }
            });
          } else {
            await tx.basicProjectInfoLedger.updateMany({
              where: {
                orgId,
                tenantId,
                basicProjectInfoFieldDetailId: item.id
              },
              data: {
                value: item.value,
                updateBy: userId
              }
            });
          }
        })
      );
    });
    return true;
  }

  async getList(basicProjectInfoCategoryId: string) {
    return await this.prisma.basicProjectInfoFieldDetail.findMany({
      select: {
        id: true,
        name: true,
        type: true,
        description: true,
        placeholder: true,
        enumValue: true,
        status: true,
        unit: true,
        isRequired: true,
        isDefault: true
      },
      where: {
        basicProjectInfoCategoryId,
        isDeleted: false
      },
      orderBy: {
        sort: 'asc'
      }
    });
  }

  // 查询字段是否使用
  async getUseList(basicProjectInfoCategoryId: string) {
    return await this.prisma.basicProjectInfoFieldDetail.findMany({
      select: {
        id: true,
        name: true,
        type: true,
        description: true,
        placeholder: true,
        enumValue: true,
        status: true,
        unit: true,
        isRequired: true,
        isDefault: true
      },
      where: {
        basicProjectInfoCategoryId,
        isDeleted: false
      },
      orderBy: {
        sort: 'asc'
      }
    });
  }

  async getListAll(req: Request, reqUser: IReqUser) {
    // 查询公司组织的顶层id
    const topOrgId = await this.platformService.getTopOrg(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    const data = await this.prisma.basicProjectInfoCategory.findMany({
      select: {
        id: true,
        name: true,
        basicProjectInfoFieldDetail: {
          select: {
            id: true,
            name: true,
            type: true,
            description: true,
            placeholder: true,
            enumValue: true,
            status: true,
            unit: true,
            isRequired: true,
            isDefault: true,
            BasicProjectInfoLedger: {
              select: {
                id: true,
                value: true
              },
              where: {
                isDeleted: false,
                orgId: reqUser.orgId,
                tenantId: reqUser.tenantId
              }
            }
          },
          where: {
            isDeleted: false,
            status: EnableStatus.ENABLED
          },
          orderBy: {
            sort: 'asc'
          }
        }
      },
      where: {
        tenantId: reqUser.tenantId,
        isDeleted: false,
        orgId: topOrgId
        // isPublish: true
      },
      orderBy: {
        sort: 'asc'
      }
    });
    return data.map((category) => ({
      ...category,
      basicProjectInfoFieldDetail: category.basicProjectInfoFieldDetail.map(
        (field) => {
          const { BasicProjectInfoLedger, ...fieldData } = field;
          return {
            value: field.BasicProjectInfoLedger.length
              ? field.BasicProjectInfoLedger[0].value
              : null,
            ...fieldData,
            enumValueList:
              field.type === BasicProjectFieldType.ENUM_MULTIPLE_CHOICE ||
              field.type === BasicProjectFieldType.ENUM_SINGLE_CHOICE
                ? field.enumValue?.split(',').map((item) => {
                    return { label: item, value: item };
                  })
                : field.enumValue
          };
        }
      )
    }));
  }

  async getObj(id: string) {
    return await this.prisma.basicProjectInfoFieldDetail.findFirst({
      select: {
        id: true,
        name: true,
        type: true,
        description: true,
        placeholder: true,
        enumValue: true,
        status: true,
        unit: true,
        isRequired: true,
        isDefault: true,
        basicProjectInfoCategoryId: true,
        basicProjectInfoCategory: {
          select: {
            isPublish: true
          }
        }
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async del(id: string, reqUser: IReqUser) {
    // 删除校验
    await this.checkDeleteConstraint(id);
    return await this.prisma.basicProjectInfoFieldDetail.update({
      where: {
        id
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  async checkDeleteConstraint(id: string) {
    const detail = await this.getObj(id);
    if (!detail) {
      throw new HttpException('字段明细不存在', HttpStatus.BAD_REQUEST);
    }
    if (detail?.basicProjectInfoCategory?.isPublish === true) {
      throw new HttpException(
        '已发布分组下的字段不可删除',
        HttpStatus.BAD_REQUEST
      );
    }

    await this.getFieldReference(id);

    // const category = await this.projectBasicInfoCategoryService.getObj(
    //   detail?.basicProjectInfoCategoryId
    // );
    // if (category?.isPublish === true) {
    //   throw new HttpException(
    //     '已发布分组下的字段不可删除',
    //     HttpStatus.BAD_REQUEST
    //   );
    // }
  }

  // 根据字段id查询字段引用
  async getFieldReference(id: string) {
    const fieldReference = await this.prisma.basicProjectInfoLedger.findFirst({
      where: {
        basicProjectInfoFieldDetailId: id
      }
    });
    if (fieldReference) {
      throw new HttpException('已使用的字段不可删除', HttpStatus.BAD_REQUEST);
    }
  }

  // 上移
  async up(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    await this.prisma.$transaction(async (tx) => {
      // 查询当前分组
      const detail = await tx.basicProjectInfoFieldDetail.findUnique({
        where: {
          id,
          isDeleted: false
        }
      });
      if (detail) {
        // 查询要下移的模版的id
        const detailDown = await tx.basicProjectInfoFieldDetail.findFirst({
          where: {
            sort: {
              lt: detail.sort
            },
            tenantId,
            orgId,
            isDeleted: false
          },
          orderBy: {
            sort: 'desc'
          }
        });
        if (!detailDown?.id) {
          throw new BadRequestException('无法上移');
        }
        // 当前排序修改
        await tx.basicProjectInfoFieldDetail.update({
          where: {
            id
          },
          data: {
            sort: detailDown.sort,
            updateBy: reqUser.id
          }
        });
        // 上一模版修改
        await tx.basicProjectInfoFieldDetail.update({
          where: {
            id: detailDown.id
          },
          data: {
            sort: detail.sort,
            updateBy: reqUser.id
          }
        });
      }
    });
  }

  // 下移
  async down(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    await this.prisma.$transaction(async (tx) => {
      // 查询当前模版
      const detail = await tx.basicProjectInfoFieldDetail.findUnique({
        where: {
          id,
          isDeleted: false
        }
      });
      if (detail) {
        // 查询要上移的模版的id
        const detailDown = await tx.basicProjectInfoFieldDetail.findFirst({
          where: {
            sort: {
              gt: detail.sort
            },
            tenantId,
            orgId,
            isDeleted: false
          },
          orderBy: {
            sort: 'asc'
          }
        });
        if (!detailDown?.id) {
          throw new BadRequestException('无法下移');
        }
        // 当前模版
        await tx.basicProjectInfoFieldDetail.update({
          where: {
            id
          },
          data: {
            sort: detailDown.sort,
            updateBy: reqUser.id
          }
        });
        // 互换的模版
        await tx.basicProjectInfoFieldDetail.update({
          where: {
            id: detailDown.id
          },
          data: {
            sort: detail.sort,
            updateBy: reqUser.id
          }
        });
      }
    });
  }

  // 启用
  async enable(
    id: string,
    reqUser: IReqUser,
    data: UpdateProjectBasicDetailsEnableDto
  ) {
    await this.prisma.basicProjectInfoFieldDetail.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        status: data.status as EnableStatus,
        updateBy: reqUser.id
      }
    });
    return true;
  }
}
