import { BadRequestException, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ProjectService } from '@/common/modules/prisma/seed/services';

import {
  CreateProjectBasicInfoCategoryDto,
  UpdateProjectBasicInfoCategoryDto,
  UpdateProjectBasicPublishTypeDto
} from './project-basic-info-category.dto';

@Injectable()
export class ProjectBasicInfoCategoryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly projectService: ProjectService
  ) {}

  async getObjByName(reqUser: IReqUser, name: string) {
    return await this.prisma.basicProjectInfoCategory.findFirst({
      where: {
        name,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
  }

  async getObjByNameNotInOwer(id: string, reqUser: IReqUser, name: string) {
    return await this.prisma.basicProjectInfoCategory.findFirst({
      where: {
        id: {
          not: id
        },
        name,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
  }

  async add(data: CreateProjectBasicInfoCategoryDto, reqUser: IReqUser) {
    // 查询最大的sort
    const maxSort = await this.getMaxSort(reqUser);
    return await this.prisma.basicProjectInfoCategory.create({
      data: {
        ...data,
        sort: maxSort,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
  }

  async getObj(id: string) {
    return await this.prisma.basicProjectInfoCategory.findFirst({
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async edit(
    id: string,
    data: UpdateProjectBasicInfoCategoryDto,
    reqUser: IReqUser
  ) {
    return await this.prisma.basicProjectInfoCategory.update({
      where: {
        id
      },
      data: {
        ...data,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        updateBy: reqUser.id
      }
    });
  }

  async getList(reqUser: IReqUser) {
    await this.prisma.$transaction(async (txPrisma) => {
      await this.projectService.init(txPrisma as PrismaService, reqUser);
    });

    return await this.prisma.basicProjectInfoCategory.findMany({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      orderBy: {
        sort: 'asc'
      }
    });
  }

  async del(id: string, reqUser: IReqUser) {
    return await this.prisma.basicProjectInfoCategory.update({
      where: {
        id
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }

  // 查询最大的排序
  async getMaxSort(reqUser: IReqUser) {
    const maxSort = await this.prisma.basicProjectInfoCategory.aggregate({
      _max: {
        sort: true
      },
      where: {
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
    return maxSort?._max?.sort ? maxSort?._max?.sort + 1 : 1;
  }

  // 上移
  async up(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    await this.prisma.$transaction(async (tx) => {
      // 查询当前分组
      const category = await tx.basicProjectInfoCategory.findUnique({
        where: {
          id,
          isDeleted: false
        }
      });
      if (category) {
        // 查询要下移的模版的id
        const categoryDown = await tx.basicProjectInfoCategory.findFirst({
          where: {
            sort: {
              lt: category.sort
            },
            tenantId,
            orgId,
            isDeleted: false
          },
          orderBy: {
            sort: 'desc'
          }
        });
        if (!categoryDown?.id) {
          throw new BadRequestException('无法上移');
        }
        // 当前排序修改
        await tx.basicProjectInfoCategory.update({
          where: {
            id
          },
          data: {
            sort: categoryDown.sort,
            updateBy: reqUser.id
          }
        });
        // 上一模版修改
        await tx.basicProjectInfoCategory.update({
          where: {
            id: categoryDown.id
          },
          data: {
            sort: category.sort,
            updateBy: reqUser.id
          }
        });
      }
    });
  }

  // 下移
  async down(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    await this.prisma.$transaction(async (tx) => {
      // 查询当前模版
      const category = await tx.basicProjectInfoCategory.findUnique({
        where: {
          id,
          isDeleted: false
        }
      });
      if (category) {
        // 查询要上移的模版的id
        const categoryDown = await tx.basicProjectInfoCategory.findFirst({
          where: {
            sort: {
              gt: category.sort
            },
            tenantId,
            orgId,
            isDeleted: false
          },
          orderBy: {
            sort: 'asc'
          }
        });
        if (!categoryDown?.id) {
          throw new BadRequestException('无法下移');
        }
        // 当前模版
        await tx.basicProjectInfoCategory.update({
          where: {
            id
          },
          data: {
            sort: categoryDown.sort,
            updateBy: reqUser.id
          }
        });
        // 互换的模版
        await tx.basicProjectInfoCategory.update({
          where: {
            id: categoryDown.id
          },
          data: {
            sort: category.sort,
            updateBy: reqUser.id
          }
        });
      }
    });
  }

  async publish(reqUser: IReqUser, data: UpdateProjectBasicPublishTypeDto) {
    await this.prisma.basicProjectInfoCategory.updateMany({
      where: {
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async getStatus(reqUser: IReqUser) {
    const total = await this.prisma.basicProjectInfoCategory.count({
      where: {
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
    const publishTotal = await this.prisma.basicProjectInfoCategory.count({
      where: {
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isPublish: true
      }
    });
    return total === publishTotal ? true : false;
  }
}
