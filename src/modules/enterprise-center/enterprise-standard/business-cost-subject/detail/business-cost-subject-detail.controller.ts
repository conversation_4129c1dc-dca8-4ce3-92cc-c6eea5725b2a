import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  BusinessCostSubjectDetailCreateDto,
  BusinessCostSubjectDetailQueryListDto,
  BusinessCostSubjectDetailResultDto,
  BusinessCostSubjectDetailTreeResultDto,
  BusinessCostSubjectDetailUpdateDto
} from './business-cost-subject-detail.dto';
import { BusinessCostSubjectDetailService } from './business-cost-subject-detail.service';

@ApiTags('业务成本科目/明细')
@Controller('business-cost-subject-detail')
export class BusinessCostSubjectDetailController {
  constructor(private readonly service: BusinessCostSubjectDetailService) {}

  @ApiOperation({
    summary: '获取明细列表',
    description: '获取明细列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取明细列表成功',
    type: BusinessCostSubjectDetailResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @Query() query: BusinessCostSubjectDetailQueryListDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const list = await this.service.getList(reqUser, query);
    return list;
  }

  @ApiOperation({
    summary: '获取明细树(分类+明细)',
    description: '获取明细树(分类+明细)'
  })
  @ApiResponse({
    status: 200,
    description: '获取明细树成功',
    type: BusinessCostSubjectDetailTreeResultDto,
    isArray: true
  })
  @Get('_tree')
  async getTree(
    @ReqUser() reqUser: IReqUser,
    @Query('businessCostSubjectVersionId') businessCostSubjectVersionId: string
  ) {
    const list = await this.service.getTree(
      reqUser,
      businessCostSubjectVersionId
    );
    return list;
  }

  @ApiOperation({
    summary: '创建明细',
    description: '创建明细'
  })
  @ApiResponse({
    status: 200,
    description: '创建明细成功',
    type: Boolean
  })
  @Post()
  async createOne(
    @Body() data: BusinessCostSubjectDetailCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.createOne(reqUser, data);
    return true;
  }

  @ApiOperation({
    summary: '更新明细',
    description: '更新明细'
  })
  @ApiResponse({
    status: 200,
    description: '更新明细成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: BusinessCostSubjectDetailUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(reqUser, id, data);
    return true;
  }

  @ApiOperation({
    summary: '移动明细',
    description: '移动明细'
  })
  @ApiQuery({
    name: 'moveTo',
    description: '移动至',
    required: true,
    enum: MoveToEnum
  })
  @ApiResponse({
    status: 200,
    description: '移动明细成功',
    type: Boolean
  })
  @Patch(':id/_move')
  async moveOne(
    @Param('id') id: string,
    @Query('moveTo') moveTo: MoveToEnum,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.moveOne(reqUser, id, moveTo);
    return true;
  }

  @ApiOperation({
    summary: '删除明细',
    description: '删除明细'
  })
  @ApiResponse({
    status: 200,
    description: '删除明细成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(reqUser, id);
    return true;
  }
}
