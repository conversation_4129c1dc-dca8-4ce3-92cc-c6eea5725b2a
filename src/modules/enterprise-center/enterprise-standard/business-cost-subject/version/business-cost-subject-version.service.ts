import { BadRequestException, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { EnableStatus } from '@/prisma/generated';

import {
  BusinessCostSubjectVersionCreateDto,
  BusinessCostSubjectVersionResultDto,
  BusinessCostSubjectVersionSearchListDto,
  BusinessCostSubjectVersionUpdateDto
} from './business-cost-subject-version.dto';

@Injectable()
export class BusinessCostSubjectVersionService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly platformService: PlatformService
  ) {}

  async getList(
    reqUser: IReqUser,
    query: BusinessCostSubjectVersionSearchListDto
  ): Promise<BusinessCostSubjectVersionResultDto[]> {
    const { tenantId, orgId } = reqUser;
    const { status } = query;

    // TODO: 根据用户查看权限进行数据过滤
    // TODO：补充项目引用数量、字典引用数量字段逻辑

    // 拿到当前组织的id查询该组织所有上级层级，
    // const parentOrgList = await this.platformService.getOrgParentIds(
    //   tenantId,
    //   orgId
    // );

    // const orgIds: string[] = parentOrgList;

    const recordList = await this.prisma.businessCostSubjectVersion.findMany({
      select: {
        id: true,
        name: true,
        status: true,
        orgId: true
      },
      where: {
        tenantId,
        // orgId: {
        //   in: orgIds
        // },
        orgId,
        ...(status && { status: status }),
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });

    // 并行获取每个版本的独立引用量
    const refCounts = await Promise.all(
      recordList.map((record) =>
        this.getRefInfo(tenantId, record.orgId, record.id)
      )
    );

    // 获取所有版本的引用数量
    const refProjectCountsList = await this.getAllProjectRefInfo(tenantId);

    const list = recordList.map((item, index) => ({
      ...item,
      isOwner: item.orgId === reqUser.orgId ? true : false,
      dictRefCount: refCounts[index].dictRefCount,
      projectRefCount:
        refProjectCountsList.find((item1) => item1.versionId === item.id)
          ?._count._all || 0
    }));

    return list;
  }

  async createOne(
    reqUser: IReqUser,
    data: BusinessCostSubjectVersionCreateDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查数据是否重复
    await this.checkUnique(tenantId, orgId, data);

    await this.prisma.businessCostSubjectVersion.create({
      data: {
        ...data,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async updateOne(
    reqUser: IReqUser,
    id: string,
    data: BusinessCostSubjectVersionUpdateDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查数据是否重复
    if (data.name != null) {
      await this.checkUnique(tenantId, orgId, data);
    }

    // 停用时，判断当前版本数据是否被引用，如果没有被引用，将状态改为“未启用”
    if (data.status === EnableStatus.NOT_ENABLED) {
      const refInfo = (await this.getRefInfo(tenantId, orgId, id)) as {
        dictRefCount: number;
      };
      const projectRef = (await this.getProjectRefInfo(
        tenantId,
        orgId,
        id
      )) as {
        projectRefCount: number;
      };
      // 如果没有被引用，将状态改为“未启用”
      if (refInfo.dictRefCount === 0 && projectRef.projectRefCount === 0) {
        data.status = EnableStatus.NOT_ENABLED;
      }
      // 如果存在引用，将状态修改为“已停用”
      if (refInfo.dictRefCount !== 0 || projectRef.projectRefCount !== 0) {
        data.status = EnableStatus.DISABLED;
      }
    }

    await this.prisma.businessCostSubjectVersion.update({
      where: {
        tenantId,
        orgId,
        id
      },
      data: {
        ...data,
        updateBy: userId
      }
    });
  }

  async deleteOne(reqUser: IReqUser, id: string) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    await this.prisma.businessCostSubjectVersion.update({
      where: {
        tenantId,
        orgId,
        id
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(
    tenantId: string,
    orgId: string,
    data:
      | BusinessCostSubjectVersionCreateDto
      | BusinessCostSubjectVersionUpdateDto
  ) {
    if (!data.name) {
      return;
    }

    const duplicateRecord =
      await this.prisma.businessCostSubjectVersion.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          name: data.name,
          isDeleted: false
        }
      });

    if (duplicateRecord) {
      throw new BadRequestException('名称重复，请重新输入！');
    }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    // 启用、停用状态的版本不能删除
    const currentRecord =
      await this.prisma.businessCostSubjectVersion.findUnique({
        select: {
          status: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      });
    if (currentRecord && currentRecord.status === EnableStatus.ENABLED) {
      throw new BadRequestException('启用状态的版本不可删除！');
    }
    if (currentRecord && currentRecord.status === EnableStatus.DISABLED) {
      throw new BadRequestException('停用状态的版本不可删除！');
    }

    const projectRef = (await this.getProjectRefInfo(tenantId, orgId, id)) as {
      projectRefCount: number;
    };
    if (projectRef.projectRefCount > 0) {
      throw new BadRequestException('被引用的版本不可删除！');
    }

    // 存在分类数据的版本不能删除
    const category = await this.prisma.businessCostSubjectCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectVersionId: id,
        isDeleted: false
      }
    });
    if (category) {
      throw new BadRequestException('该版本下存在数据，不可删除！');
    }
  }

  /**
   * 获取字典引用信息
   */
  private async getRefInfo(
    tenantId: string,
    orgId: string,
    id?: string
  ): Promise<{ dictRefCount: number }> {
    // 标准外键名称
    const FOREIGN_KEY = 'businessCostSubjectVersionId';
    // 扩展配置
    const dictModels = [this.prisma.materialDictionaryVersion];

    // 精准统计逻辑：当 id 存在时，只统计该版本的引用
    const results = await Promise.all(
      dictModels.map((model) =>
        model.count({
          where: {
            tenantId,
            orgId,
            isDeleted: false,
            ...(id && { [FOREIGN_KEY]: id }) // 有 id 时精准过滤
          }
        })
      )
    );

    const dictTotalRefCount = results.reduce((sum, count) => sum + count, 0);

    return { dictRefCount: dictTotalRefCount };
  }

  // 获取某个版本的项目引用数
  async getProjectRefInfo(
    tenantId: string,
    orgId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountBusinessCostSubjectVersion.count({
        where: {
          tenantId,
          isDeleted: false,
          versionId: id
        }
      });
    return { projectRefCount };
  }

  // 获取所有的版本的项目引用数，按版本分组统计
  async getAllProjectRefInfo(tenantId: string) {
    // 项目引用数统计
    return await this.prisma.accountBusinessCostSubjectVersion.groupBy({
      where: {
        tenantId,
        isDeleted: false
      },
      _count: {
        _all: true
      },
      by: ['versionId']
    });
  }
}
