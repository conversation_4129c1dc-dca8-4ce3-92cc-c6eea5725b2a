import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { v7 as uuidv7 } from 'uuid';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { BusinessCostSubjectCategory, EnableStatus } from '@/prisma/generated';
import { BusinessCostSubjectCategoryWhereInput } from '@/prisma/generated/models';

import { BusinessCostSubjectVersionService } from '../version/business-cost-subject-version.service';
import {
  BusinessCostSubjectCategoryCreateDto,
  BusinessCostSubjectCategoryResultDto,
  BusinessCostSubjectCategoryUpdateDto
} from './business-cost-subject-category.dto';

@Injectable()
export class BusinessCostSubjectCategoryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly businessCostSubjectVersionService: BusinessCostSubjectVersionService
  ) {}

  async getList(
    reqUser: IReqUser,
    versionId: string
  ): Promise<BusinessCostSubjectCategoryResultDto[]> {
    const { tenantId, orgId } = reqUser;

    const list = await this.prisma.businessCostSubjectCategory.findMany({
      select: {
        businessCostSubjectVersionId: true,
        id: true,
        parentId: true,
        code: true,
        name: true,
        remark: true,
        isActive: true,
        sort: true,
        isLeaf: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectVersionId: versionId,
        isDeleted: false
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });

    return list;
  }

  async createOne(
    reqUser: IReqUser,
    data: BusinessCostSubjectCategoryCreateDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.businessCostSubjectVersionId);

    // 检查数据是否重复
    await this.checkUnique({ tenantId, orgId, data });

    const newRecord = {
      ...data,
      tenantId,
      orgId,
      id: uuidv7(),
      createBy: userId,
      updateBy: userId
    } as BusinessCostSubjectCategory;

    await this.processTreeInfo(tenantId, orgId, newRecord);

    await this.prisma.businessCostSubjectCategory.create({
      data: {
        ...newRecord,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async updateOne(
    reqUser: IReqUser,
    id: string,
    data: BusinessCostSubjectCategoryUpdateDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category?.businessCostSubjectVersionId);

    // 检查数据是否重复
    if (data.name !== category.name || data.code !== category.code) {
      await this.checkUnique({
        tenantId,
        orgId,
        data,
        id,
        businessCostSubjectVersionId: category.businessCostSubjectVersionId
      });
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.businessCostSubjectCategory.update({
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        },
        data: {
          ...data,
          updateBy: userId
        }
      });
      // 启用/废弃时，级联更新子级分类、明细的启用状态
      if (data.isActive != null) {
        const childCategoryList =
          await tx.businessCostSubjectCategory.updateManyAndReturn({
            select: {
              id: true
            },
            where: {
              tenantId,
              orgId,
              id: { not: id },
              fullId: { contains: id },
              isDeleted: false
            },
            data: {
              isActive: data.isActive,
              updateBy: userId
            }
          });
        const categoryIds = [id, ...childCategoryList.map((i) => i.id)];
        await tx.businessCostSubjectDetail.updateMany({
          where: {
            tenantId,
            orgId,
            businessCostSubjectCategoryId: { in: categoryIds },
            isDeleted: false
          },
          data: {
            isActive: data.isActive,
            updateBy: userId
          }
        });
      }
    });
  }

  async moveOne(reqUser: IReqUser, id: string, moveTo: MoveToEnum) {
    const { tenantId, orgId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    await this.checkVersionStatus(category?.businessCostSubjectVersionId);

    const currentRecord =
      await this.prisma.businessCostSubjectCategory.findFirst({
        select: {
          id: true,
          businessCostSubjectVersionId: true,
          parentId: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      businessCostSubjectVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { businessCostSubjectVersionId } = currentRecord;

    const prevRecordWhere: BusinessCostSubjectCategoryWhereInput = {
      tenantId,
      orgId,
      businessCostSubjectVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        lt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      prevRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到上一条数据
    const prevRecord = await this.prisma.businessCostSubjectCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: prevRecordWhere,
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.businessCostSubjectCategory.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: currentRecord.id
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.businessCostSubjectCategory.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: prevRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      businessCostSubjectVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { businessCostSubjectVersionId } = currentRecord;

    const nextRecordWhere: BusinessCostSubjectCategoryWhereInput = {
      tenantId,
      orgId,
      businessCostSubjectVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        gt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      nextRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到下一条数据
    const nextRecord = await this.prisma.businessCostSubjectCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: nextRecordWhere,
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.businessCostSubjectCategory.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: currentRecord.id
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.businessCostSubjectCategory.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: nextRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.businessCostSubjectVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.businessCostSubjectCategory.findUnique({
      select: {
        businessCostSubjectVersionId: true,
        parentId: true,
        name: true,
        code: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async deleteOne(reqUser: IReqUser, id: string) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    await this.checkVersionStatus(category?.businessCostSubjectVersionId);

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    // 校验版本是否被引用
    const projectRefCount =
      await this.businessCostSubjectVersionService.getProjectRefInfo(
        tenantId,
        orgId,
        category?.businessCostSubjectVersionId
      );
    if (projectRefCount.projectRefCount > 0) {
      throw new HttpException(
        `该分类版本已被项目引用，无法删除`,
        HttpStatus.BAD_REQUEST
      );
    }

    const ids = [id];

    // 如果有子级，则级联删除
    const children = await this.prisma.businessCostSubjectCategory.findMany({
      select: {
        id: true,
        parentId: true
      },
      where: {
        tenantId,
        orgId,
        id,
        isDeleted: false
      }
    });

    ids.push(...children.map((item) => item.id));

    await this.prisma.$transaction(async (tx) => {
      await tx.businessCostSubjectCategory.updateMany({
        where: {
          tenantId,
          orgId,
          id: { in: ids }
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      // 删除分类后查询是否有父级分类，有则改变父级分类状态为非叶子节点
      await this.updateParentLeaf(
        tx as PrismaService,
        category.parentId || null,
        orgId,
        tenantId,
        reqUser.id
      );
    });
  }

  async updateParentLeaf(
    tx: PrismaService,
    parentId: string | null,
    orgId: string,
    tenantId: string,
    userId: string
  ) {
    if (parentId) {
      // 查询父级
      const parentCategory = await tx.businessCostSubjectCategory.findFirst({
        where: {
          id: parentId,
          isDeleted: false,
          orgId,
          tenantId
        },
        select: {
          id: true,
          children: {
            select: {
              id: true
            },
            where: {
              isDeleted: false
            }
          }
        }
      });
      if (!parentCategory?.children.length) {
        await tx.businessCostSubjectCategory.update({
          where: {
            id: parentId
          },
          data: {
            isLeaf: true,
            updateBy: userId,
            updateAt: new Date()
          }
        });
      }
    }
  }

  /**
   * 处理树形信息
   */
  private async processTreeInfo(
    tenantId: string,
    orgId: string,
    data: BusinessCostSubjectCategory
  ) {
    // 查询同级节点的最后一个数据，用于给当前数据生成排序号
    const findSiblingNodeLastWhereInput: BusinessCostSubjectCategoryWhereInput =
      {
        tenantId,
        orgId,
        businessCostSubjectVersionId: data.businessCostSubjectVersionId,
        isDeleted: false
      };
    if (data.parentId) {
      findSiblingNodeLastWhereInput.parentId = data.parentId;
    }
    const maxSort = await this.prisma.businessCostSubjectCategory.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectVersionId: data.businessCostSubjectVersionId,
        parentId: data.parentId,
        isDeleted: false
      }
    });
    data.sort = (maxSort._max.sort || 0) + 1;

    if (data.parentId) {
      const parent = await this.prisma.businessCostSubjectCategory.findFirst({
        select: {
          fullId: true,
          fullName: true,
          level: true
        },
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId: data.businessCostSubjectVersionId,
          id: data.parentId,
          isDeleted: false
        }
      });
      if (!parent) {
        throw new BadRequestException('父级不存在');
      }

      data.fullId = `${parent.fullId}/${data.id}`;
      data.fullName = `${parent.fullName}/${data.name}`;
      data.level = parent.level + 1;
    } else {
      data.fullId = data.id;
      data.fullName = data.name;
    }
    // 默认初始都为叶子节点,存在父级数据后修改
    data.isLeaf = true;
    if (data.parentId) {
      await this.prisma.businessCostSubjectCategory.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId: data.businessCostSubjectVersionId,
          id: data.parentId
        },
        data: {
          isLeaf: false
        }
      });
    }
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data:
      | BusinessCostSubjectCategoryCreateDto
      | BusinessCostSubjectCategoryUpdateDto;
    id?: string;
    businessCostSubjectVersionId?: string;
  }) {
    const { tenantId, orgId, data, id, businessCostSubjectVersionId } = args;

    const code = data.code;
    const name = data.name;

    const duplicateCodeRecod =
      await this.prisma.businessCostSubjectCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId: data.businessCostSubjectVersionId
            ? data.businessCostSubjectVersionId
            : businessCostSubjectVersionId,
          id: { not: id },
          code,
          isDeleted: false
        }
      });
    const duplicateNameRecod =
      await this.prisma.businessCostSubjectCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId: data.businessCostSubjectVersionId
            ? data.businessCostSubjectVersionId
            : businessCostSubjectVersionId,
          id: { not: id },
          name,
          isDeleted: false
        }
      });
    if (duplicateCodeRecod) {
      throw new BadRequestException('编码重复，请重新输入！');
    }
    if (duplicateNameRecod) {
      throw new BadRequestException('名称重复，请重新输入！');
    }

    // const duplicateRecord =
    //   await this.prisma.businessCostSubjectCategory.findFirst({
    //     select: {
    //       id: true
    //     },
    //     where: {
    //       tenantId,
    //       orgId,
    //       businessCostSubjectVersionId: data.businessCostSubjectVersionId,
    //       id: { not: id },
    //       code,
    //       name,
    //       isDeleted: false
    //     }
    //   });

    // if (duplicateRecord) {
    //   throw new BadRequestException('编码+名称重复，请重新输入！');
    // }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    const childLevel = await this.prisma.businessCostSubjectCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        parentId: id,
        isDeleted: false
      }
    });
    if (childLevel) {
      throw new BadRequestException('分类下存在下级分类，不可删除！');
    }
    const detail = await this.prisma.businessCostSubjectDetail.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectCategoryId: id,
        isDeleted: false
      }
    });

    if (detail) {
      throw new BadRequestException('分类下存在明细，不可删除！');
    }
  }
}
