import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialDictionaryDetailCreateDto,
  MaterialDictionaryDetailQueryParamDto,
  MaterialDictionaryDetailResultDto,
  MaterialDictionaryDetailSearchListDto,
  MaterialDictionaryDetailUpdateDto
} from './material-dictionary-detail.dto';
import { MaterialDictionaryDetailService } from './material-dictionary-detail.service';

@ApiTags('材料字典/明细')
@Controller('material-dictionary-detail')
export class MaterialDictionaryDetailController {
  constructor(private readonly service: MaterialDictionaryDetailService) {}

  @ApiOperation({
    summary: '获取明细列表',
    description: '获取明细列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取明细列表成功',
    type: MaterialDictionaryDetailResultDto,
    isArray: true
  })
  @Get()
  async getList(
    @Query() queryParam: MaterialDictionaryDetailQueryParamDto,
    @ReqUser() reqUser: IReqUser
  ) {
    const list = await this.service.getList(reqUser, queryParam);
    return list;
  }

  @ApiOperation({
    summary: '搜索明细列表',
    description: '搜索明细列表'
  })
  @ApiResponse({
    status: 200,
    description: '搜索明细列表成功',
    type: MaterialDictionaryDetailResultDto,
    isArray: true
  })
  @Get('_search')
  async search(
    @ReqUser() reqUser: IReqUser,
    @Query() query: MaterialDictionaryDetailSearchListDto
  ) {
    const list = await this.service.search(reqUser, query);
    return list;
  }

  @ApiOperation({
    summary: '创建明细',
    description: '创建明细'
  })
  @ApiResponse({
    status: 200,
    description: '创建明细成功',
    type: Boolean
  })
  @Post()
  async createOne(
    @Body() data: MaterialDictionaryDetailCreateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.createOne(reqUser, data);
    return true;
  }

  @ApiOperation({
    summary: '更新明细',
    description: '更新明细'
  })
  @ApiResponse({
    status: 200,
    description: '更新明细成功',
    type: Boolean
  })
  @Patch(':id')
  async updateOne(
    @Param('id') id: string,
    @Body() data: MaterialDictionaryDetailUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.updateOne(id, data, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '删除明细',
    description: '删除明细'
  })
  @ApiResponse({
    status: 200,
    description: '更新明细成功',
    type: Boolean
  })
  @Delete(':id')
  async deleteOne(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    await this.service.deleteOne(id, reqUser);
    return true;
  }

  @ApiOperation({
    summary: '移动明细',
    description: '移动明细'
  })
  @ApiResponse({
    status: 200,
    description: '移动成功',
    type: Boolean
  })
  @Patch(':id/_move')
  async moveOne(
    @Param('id') id: string,
    @Query('moveTo') moveTo: MoveToEnum,
    @ReqUser() reqUser: IReqUser
  ) {
    await this.service.moveOne(id, moveTo, reqUser);
    return true;
  }
}
