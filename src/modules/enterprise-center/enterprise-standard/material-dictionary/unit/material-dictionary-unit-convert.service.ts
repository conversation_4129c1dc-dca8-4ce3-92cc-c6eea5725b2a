import { BadRequestException, Injectable } from '@nestjs/common';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  MaterialDictionaryUnitConvertCreateDto,
  MaterialDictionaryUnitConvertQueryParamDto,
  MaterialDictionaryUnitConvertUpdateDto
} from './material-dictionary-unit-convert.dto';

@Injectable()
export class MaterialDictionaryUnitConvertService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    queryParam: MaterialDictionaryUnitConvertQueryParamDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId } = reqUser;
    const { materialDictionaryDetailId } = queryParam;

    if (!materialDictionaryDetailId) {
      return [];
    }

    const list = await this.prisma.materialDictionaryUnitCalculation.findMany({
      select: {
        id: true,
        materialDictionaryDetailId: true,
        unit: true,
        factor: true,
        remark: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        materialDictionaryDetailId,
        isDeleted: false
      },
      orderBy: [{ sort: 'asc' }]
    });

    return list;
  }

  async createOne(
    data: MaterialDictionaryUnitConvertCreateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    const maxSort =
      await this.prisma.materialDictionaryUnitCalculation.aggregate({
        _max: {
          sort: true
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId: data.materialDictionaryDetailId,
          isDeleted: false
        }
      });

    await this.prisma.materialDictionaryUnitCalculation.create({
      data: {
        ...data,
        sort: (maxSort._max.sort || 0) + 1,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async updateOne(
    id: string,
    data: MaterialDictionaryUnitConvertUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    await this.prisma.materialDictionaryUnitCalculation.update({
      where: {
        id,
        tenantId,
        orgId
      },
      data: {
        ...data,
        updateBy: userId
      }
    });
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    await this.prisma.materialDictionaryUnitCalculation.update({
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }

  async moveOne(reqUser: IReqUser, id: string, moveTo: MoveToEnum) {
    const { tenantId, orgId } = reqUser;

    const currentRecord =
      await this.prisma.materialDictionaryUnitCalculation.findFirst({
        select: {
          id: true,
          materialDictionaryDetailId: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      });
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      materialDictionaryDetailId: string;
      sort: number;
    }
  ) {
    const { materialDictionaryDetailId } = currentRecord;

    // 找到上一条数据
    const prevRecord =
      await this.prisma.materialDictionaryUnitCalculation.findFirst({
        select: {
          id: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId,
          isDeleted: false,
          id: {
            not: currentRecord.id
          },
          sort: {
            lt: currentRecord.sort
          }
        },
        orderBy: {
          sort: 'desc'
        }
      });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.materialDictionaryUnitCalculation.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId,
          id: currentRecord.id
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.materialDictionaryUnitCalculation.update({
        where: {
          tenantId,
          orgId,
          id: prevRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      materialDictionaryDetailId: string;
      sort: number;
    }
  ) {
    const { materialDictionaryDetailId } = currentRecord;

    // 找到下一条数据
    const nextRecord =
      await this.prisma.materialDictionaryUnitCalculation.findFirst({
        select: {
          id: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId,
          isDeleted: false,
          id: {
            not: currentRecord.id
          },
          sort: {
            gt: currentRecord.sort
          }
        },
        orderBy: {
          sort: 'asc'
        }
      });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.materialDictionaryUnitCalculation.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId,
          id: currentRecord.id
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.materialDictionaryUnitCalculation.update({
        where: {
          tenantId,
          orgId,
          materialDictionaryDetailId,
          id: nextRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }
}
