import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { v7 as uuidv7 } from 'uuid';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus, TaxrateDictionaryCategory } from '@/prisma/generated';
import { TaxrateDictionaryCategoryWhereInput } from '@/prisma/generated/models';

import {
  TaxrateDictionaryCategoryCreateDto,
  TaxrateDictionaryCategoryUpdateDto
} from './taxrate-dictionary-category.dto';

@Injectable()
export class TaxrateDictionaryCategoryService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(reqUser: IReqUser, versionId: string) {
    const { tenantId, orgId } = reqUser;

    const list = await this.prisma.taxrateDictionaryCategory.findMany({
      select: {
        taxrateDictionaryVersionId: true,
        id: true,
        code: true,
        name: true,
        remark: true,
        isActive: true,
        parentId: true,
        isLeaf: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryVersionId: versionId,
        isDeleted: false
      },
      orderBy: [{ isActive: 'desc' }, { sort: 'asc' }]
    });

    return list;
  }

  async createOne(data: TaxrateDictionaryCategoryCreateDto, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.taxrateDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({ tenantId, orgId, data });

    const newRecord = {
      ...data,
      tenantId,
      orgId,
      id: uuidv7(),
      createBy: userId,
      updateBy: userId
    } as TaxrateDictionaryCategory;

    await this.processTreeInfo(tenantId, orgId, newRecord);

    await this.prisma.taxrateDictionaryCategory.create({
      data: {
        ...newRecord,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.taxrateDictionaryVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string) {
    return await this.prisma.taxrateDictionaryCategory.findUnique({
      select: {
        taxrateDictionaryVersionId: true,
        parentId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async updateOne(
    id: string,
    data: TaxrateDictionaryCategoryUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.taxrateDictionaryVersionId);

    // 检查数据是否重复
    await this.checkUnique({
      tenantId,
      orgId,
      data,
      id,
      taxrateDictionaryVersionId: category.taxrateDictionaryVersionId
    });

    await this.prisma.$transaction(async (tx) => {
      await tx.taxrateDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        },
        data: {
          ...data,
          updateBy: userId
        }
      });
      if (data.isActive != null) {
        const childCategoryList =
          await tx.taxrateDictionaryCategory.updateManyAndReturn({
            select: {
              id: true
            },
            where: {
              tenantId,
              orgId,
              id: { not: id },
              fullId: { contains: id },
              isDeleted: false
            },
            data: {
              isActive: data.isActive,
              updateBy: userId
            }
          });
        const categoryIds = [id, ...childCategoryList.map((i) => i.id)];
        await tx.taxrateDictionaryDetail.updateMany({
          where: {
            tenantId,
            orgId,
            taxrateDictionaryCategoryId: { in: categoryIds },
            isDeleted: false
          },
          data: {
            isActive: data.isActive,
            updateBy: userId
          }
        });
      }
    });
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.taxrateDictionaryVersionId);

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    // 检查分类是否被引用
    const projectRefCount = await this.getProjectRefInfo(tenantId, id);
    if (projectRefCount.projectRefCount > 0) {
      throw new BadRequestException('分类被引用，无法删除！');
    }

    const ids = [id];

    // 如果有子级，则级联删除
    const children = await this.prisma.taxrateDictionaryCategory.findMany({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        id: { not: id },
        fullId: { contains: id }
      }
    });
    ids.push(...children.map((item) => item.id));

    await this.prisma.$transaction(async (tx) => {
      await tx.taxrateDictionaryCategory.updateMany({
        where: {
          tenantId,
          orgId,
          id: { in: ids }
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      // 删除分类后查询是否有父级分类，有则改变父级分类状态为非叶子节点
      await this.updateParentLeaf(
        tx as PrismaService,
        category.parentId || null,
        orgId,
        tenantId,
        reqUser.id
      );
    });
  }

  async updateParentLeaf(
    tx: PrismaService,
    parentId: string | null,
    orgId: string,
    tenantId: string,
    userId: string
  ) {
    if (parentId) {
      // 查询父级
      const parentCategory = await tx.taxrateDictionaryCategory.findFirst({
        where: {
          id: parentId,
          isDeleted: false,
          orgId,
          tenantId
        },
        select: {
          id: true,
          children: {
            select: {
              id: true
            },
            where: {
              isDeleted: false
            }
          }
        }
      });
      if (!parentCategory?.children.length) {
        await tx.taxrateDictionaryCategory.update({
          where: {
            id: parentId
          },
          data: {
            isLeaf: true,
            updateBy: userId,
            updateAt: new Date()
          }
        });
      }
    }
  }

  async moveOne(reqUser: IReqUser, id: string, moveTo: MoveToEnum) {
    const { tenantId, orgId } = reqUser;

    // 查询分类的版本
    const category = await this.getOne(id);

    if (!category) {
      throw new HttpException(`分类信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验启用状态
    await this.checkVersionStatus(category.taxrateDictionaryVersionId);

    const currentRecord = await this.prisma.taxrateDictionaryCategory.findFirst(
      {
        select: {
          id: true,
          taxrateDictionaryVersionId: true,
          parentId: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      }
    );
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  /**
   * 处理树形信息
   */
  private async processTreeInfo(
    tenantId: string,
    orgId: string,
    data: TaxrateDictionaryCategory
  ) {
    // 查询同级节点的最后一个数据，用于给当前数据生成排序号
    const findSiblingNodeLastWhereInput: TaxrateDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      taxrateDictionaryVersionId: data.taxrateDictionaryVersionId,
      isDeleted: false
    };
    if (data.parentId) {
      findSiblingNodeLastWhereInput.parentId = data.parentId;
    }
    const maxSort = await this.prisma.taxrateDictionaryCategory.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryVersionId: data.taxrateDictionaryVersionId,
        parentId: data.parentId,
        isDeleted: false
      }
    });
    data.sort = (maxSort._max.sort || 0) + 1;

    if (data.parentId) {
      const parent = await this.prisma.taxrateDictionaryCategory.findFirst({
        select: {
          fullId: true,
          fullName: true,
          level: true
        },
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId: data.taxrateDictionaryVersionId,
          id: data.parentId,
          isDeleted: false
        }
      });
      if (!parent) {
        throw new BadRequestException('父级不存在');
      }

      data.fullId = `${parent.fullId}/${data.id}`;
      data.fullName = `${parent.fullName}/${data.name}`;
      data.level = parent.level + 1;
    } else {
      data.fullId = data.id;
      data.fullName = data.name;
    }

    // 默认初始都为叶子节点,存在父级数据后修改
    data.isLeaf = true;
    if (data.parentId) {
      await this.prisma.taxrateDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId: data.taxrateDictionaryVersionId,
          id: data.parentId
        },
        data: {
          isLeaf: false
        }
      });
    }
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data:
      | TaxrateDictionaryCategoryCreateDto
      | TaxrateDictionaryCategoryUpdateDto;
    id?: string;
    taxrateDictionaryVersionId?: string;
  }) {
    const { tenantId, orgId, data, id, taxrateDictionaryVersionId } = args;

    const code = data.code;
    const name = data.name;

    const duplicateCodeRecod =
      await this.prisma.taxrateDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId: data.taxrateDictionaryVersionId
            ? data.taxrateDictionaryVersionId
            : taxrateDictionaryVersionId,
          id: { not: id },
          code,
          isDeleted: false
        }
      });
    const duplicateNameRecod =
      await this.prisma.taxrateDictionaryCategory.findFirst({
        select: {
          id: true
        },
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId: data.taxrateDictionaryVersionId
            ? data.taxrateDictionaryVersionId
            : taxrateDictionaryVersionId,
          id: { not: id },
          name,
          isDeleted: false
        }
      });
    if (duplicateCodeRecod) {
      throw new BadRequestException('编码重复，请重新输入！');
    }
    if (duplicateNameRecod) {
      throw new BadRequestException('名称重复，请重新输入！');
    }
    // const duplicateRecord =
    //   await this.prisma.taxrateDictionaryCategory.findFirst({
    //     select: {
    //       id: true
    //     },
    //     where: {
    //       tenantId,
    //       orgId,
    //       taxrateDictionaryVersionId: data.taxrateDictionaryVersionId,
    //       id: { not: id },
    //       code,
    //       name,
    //       isDeleted: false
    //     }
    //   });

    // if (duplicateRecord) {
    //   throw new BadRequestException('编码+名称重复，请重新输入！');
    // }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    const childLevel = await this.prisma.taxrateDictionaryCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        parentId: id,
        isDeleted: false
      }
    });
    if (childLevel) {
      throw new BadRequestException('分类下存在下级分类，不可删除！');
    }
    const detail = await this.prisma.taxrateDictionaryDetail.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        taxrateDictionaryCategoryId: id,
        isDeleted: false
      }
    });

    if (detail) {
      throw new BadRequestException('分类下存在明细，不可删除！');
    }
  }

  /**
   * 向上移动
   */
  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      taxrateDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { taxrateDictionaryVersionId } = currentRecord;

    const prevRecordWhere: TaxrateDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      taxrateDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        lt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      prevRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到上一条数据
    const prevRecord = await this.prisma.taxrateDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: prevRecordWhere,
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.taxrateDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.taxrateDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: prevRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  /**
   * 向下移动
   */
  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      taxrateDictionaryVersionId: string;
      parentId: string | null;
      sort: number;
    }
  ) {
    const { taxrateDictionaryVersionId } = currentRecord;

    const nextRecordWhere: TaxrateDictionaryCategoryWhereInput = {
      tenantId,
      orgId,
      taxrateDictionaryVersionId,
      isDeleted: false,
      id: {
        not: currentRecord.id
      },
      sort: {
        gt: currentRecord.sort
      }
    };
    if (currentRecord.parentId) {
      nextRecordWhere.parentId = currentRecord.parentId;
    }

    // 找到下一条数据
    const nextRecord = await this.prisma.taxrateDictionaryCategory.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: nextRecordWhere,
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.taxrateDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: currentRecord.id
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.taxrateDictionaryCategory.update({
        where: {
          tenantId,
          orgId,
          taxrateDictionaryVersionId,
          id: nextRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  // 获取分类项目引用数
  async getProjectRefInfo(
    tenantId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountTaxRateDictionaryCategory.count({
        where: {
          tenantId,
          isDeleted: false,
          categoryId: id
        }
      });
    return { projectRefCount };
  }
}
