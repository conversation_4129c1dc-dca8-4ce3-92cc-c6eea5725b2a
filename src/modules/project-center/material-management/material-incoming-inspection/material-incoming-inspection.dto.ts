import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested
} from 'class-validator';

import {
  AuditStatus,
  MaterialReceiptStatus,
  MaterialType,
  PurchaseType,
  SubmitStatus
} from '@/prisma/generated/enums';

// #region 材料验收单据
class MaterialIncomingInspectionBaseDto {
  @ApiProperty({ description: '租户ID' })
  @IsString({ message: 'tenantId 必须是字符串' })
  tenantId: string;

  @ApiProperty({ description: '组织ID' })
  @IsString({ message: 'orgId 必须是字符串' })
  orgId: string;

  @ApiProperty({ description: '数据ID，系统自动生成，可选' })
  @IsString({ message: 'id 必须是字符串' })
  id: string;

  @ApiPropertyOptional({ description: '收料状态', enum: MaterialReceiptStatus })
  @IsEnum(MaterialReceiptStatus, {
    message: 'materialReceiptStatus 必须是 MaterialReceiptStatus 枚举值'
  })
  @IsOptional()
  materialReceiptStatus?: MaterialReceiptStatus;

  @ApiProperty({ description: '单据编码' })
  @IsString({ message: 'code 必须是字符串' })
  code: string;

  @ApiProperty({ description: '采购类型', enum: PurchaseType })
  @IsEnum(PurchaseType, { message: 'purchaseType 必须是 PurchaseType 枚举值' })
  purchaseType: PurchaseType;

  @ApiProperty({ description: '供应商ID' })
  @IsString({ message: 'supplierId 必须是字符串' })
  supplierId?: string;

  @ApiPropertyOptional({ description: '供应商名称' })
  @IsString({ message: 'supplierId 必须是字符串' })
  @IsOptional()
  supplierName?: string;

  @ApiPropertyOptional({ description: '合同ID' })
  @IsString({ message: 'contractId 必须是字符串' })
  @IsOptional()
  contractId?: string;

  @ApiPropertyOptional({ description: '合同名称' })
  @IsString({ message: 'contractId 必须是字符串' })
  @IsOptional()
  contractName?: string;

  @ApiPropertyOptional({ description: '提交状态', enum: SubmitStatus })
  @IsEnum(SubmitStatus, { message: 'submitStatus 必须是 SubmitStatus 枚举值' })
  @IsOptional()
  submitStatus?: SubmitStatus;

  @ApiPropertyOptional({ description: '审批状态', enum: AuditStatus })
  @IsEnum(AuditStatus, { message: 'auditStatus 必须是 AuditStatus 枚举值' })
  @IsOptional()
  auditStatus?: AuditStatus;

  @ApiProperty({ description: '年' })
  @IsInt({ message: 'year必须是整数' })
  year: number;

  @ApiProperty({ description: '月' })
  @IsInt({ message: 'year必须是整数' })
  month: number;

  @ApiProperty({ description: '日' })
  @IsInt({ message: 'year必须是整数' })
  day: number;

  @ApiPropertyOptional({ description: '是否删除' })
  @IsBoolean({ message: 'isDeleted 必须是布尔值' })
  @IsOptional()
  isDeleted?: boolean;

  @ApiPropertyOptional({ description: '创建人' })
  @IsString({ message: 'createBy 必须是字符串' })
  @IsOptional()
  createBy?: string;

  @ApiPropertyOptional({ description: '更新人' })
  @IsString({ message: 'updateBy 必须是字符串' })
  @IsOptional()
  updateBy?: string;

  @ApiPropertyOptional({ description: '创建时间' })
  @IsDate({ message: 'createAt 必须是有效日期' })
  @Type(() => Date)
  @IsOptional()
  createAt?: Date;

  @ApiPropertyOptional({ description: '更新时间' })
  @IsDate({ message: 'updateAt 必须是有效日期' })
  @Type(() => Date)
  @IsOptional()
  updateAt?: Date;

  @ApiPropertyOptional({ description: '编制人' })
  creator?: string;

  @ApiPropertyOptional({ description: '材料类别' })
  materialCategories?: string;
}

export class InspectionTimeListResponseDto {
  @ApiProperty({ description: 'id' })
  id: string;

  @ApiPropertyOptional({ description: 'parentId' })
  parentId?: string | null;

  @ApiProperty({ description: '年' })
  @IsNumber()
  @Type(() => Number)
  year: number;

  @ApiPropertyOptional({ description: '月' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  month?: number;

  @ApiPropertyOptional({ description: '日' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  day?: number;
}

export class InspectionBillListResponseDto extends PickType(
  MaterialIncomingInspectionBaseDto,
  [
    'materialReceiptStatus',
    'id',
    'purchaseType',
    'supplierId',
    'supplierName',
    'contractId',
    'contractName',
    'materialCategories',
    'creator',
    'year',
    'month',
    'day',
    'submitStatus',
    'auditStatus'
  ] as const
) {}

export class QueryInspectionBillListDto extends PickType(
  InspectionTimeListResponseDto,
  ['year', 'month', 'day'] as const
) {
  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

export class UpdateInspectionBillListDto extends PickType(
  MaterialIncomingInspectionBaseDto,
  [
    'id',
    'purchaseType',
    'supplierId',
    'supplierName',
    'contractId',
    'contractName',
    'year',
    'month',
    'day',
    'auditStatus',
    'submitStatus'
  ] as const
) {}

class ContractItemDto {
  @ApiProperty({ description: '合同id' })
  id: string;

  @ApiProperty({ description: '合同名称' })
  name: string;
}

export class SupplierAndContractResponseDto {
  @ApiProperty({ description: '供应商id' })
  @IsString()
  id: string;

  @ApiProperty({ description: '父级id' })
  parentId?: string;

  @ApiProperty({ description: '供应商名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '合同列表' })
  contracts?: ContractItemDto[];
}

// #endregion 材料验收单据

// #region 材料验收单明细
class MaterialIncomingInspectionDetailDto {
  @ApiProperty({ description: '组织ID' })
  @IsString()
  orgId: string;

  @ApiProperty({ description: 'ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '验收单ID' })
  @IsString()
  incomingInspectionId: string;

  @ApiProperty({ description: '材料ID' })
  @IsString()
  materialId: string;

  @ApiPropertyOptional({ description: '材料名称' })
  @IsOptional()
  @IsString()
  materialName?: string;

  @ApiPropertyOptional({ description: '材料规格' })
  @IsOptional()
  @IsString()
  materialSpec?: string;

  @ApiPropertyOptional({ description: '质量标准' })
  @IsOptional()
  @IsString()
  qualityStandard?: string;

  @ApiProperty({ description: '计量单位' })
  @IsString()
  unit: string;

  @ApiProperty({ description: '进场数量' })
  @IsNumber({ maxDecimalPlaces: 8 }, { message: '最多保留8位小数' })
  @Min(0)
  @Max(99999999.99999999, { message: '不能超过99999999.99999999' })
  siteEntryQuantity?: number;

  @ApiProperty({ description: '实收数量' })
  @IsNumber({ maxDecimalPlaces: 8 }, { message: '最多保留8位小数' })
  @Min(0)
  @Max(99999999.99999999, { message: '不能超过99999999.99999999' })
  actualQuantity?: number;

  @ApiPropertyOptional({ description: '外观质量描述' })
  @IsOptional()
  @IsString()
  appearanceDescription?: string;

  @ApiProperty({ description: '排序号' })
  @IsNumber()
  orderNo: number;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  remark?: string;
}

export class InspectionBillDetailResponseDto extends PickType(
  MaterialIncomingInspectionDetailDto,
  [
    'id',
    'materialId',
    'materialName',
    'materialSpec',
    'qualityStandard',
    'unit',
    'siteEntryQuantity',
    'actualQuantity',
    'appearanceDescription',
    'remark'
  ] as const
) {
  @ApiProperty({ description: '可选单位下拉列表' })
  optionalUnits?: string;
}

export enum MaterialSearchType {
  CONTRACT = 'CONTRACT',
  MATERIAL_DICT = 'MATERIAL_DICT'
}

export class QueryMaterialCategoryListDto {
  @ApiPropertyOptional({ description: '搜索类型' })
  @IsEnum(MaterialSearchType, {
    message: 'materialSearchType 必须是 CONTRACT, 枚举值'
  })
  materialSearchType: MaterialSearchType;

  @ApiProperty({ description: '采购类型' })
  @IsEnum(PurchaseType, {
    message: 'purchaseType 必须是 PurchaseType 枚举值'
  })
  purchaseType: PurchaseType;
}

export class MaterialCategoryListResponseDto {
  @ApiProperty({ description: 'ID' })
  id: string;

  @ApiProperty({ description: '父ID' })
  parentId: string;

  @ApiProperty({ description: '类别名称' })
  name: string;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '材料类型' })
  @IsEnum(MaterialType, { message: '材料类型必须是MaterialType 枚举值' })
  type: MaterialType;

  @ApiProperty({ description: '层级' })
  level: string;

  @ApiProperty({ description: '备注' })
  remark: string;
}

export class QueryMaterialDetailListDto {
  @ApiPropertyOptional({ description: '搜索类型' })
  @IsEnum(MaterialSearchType, {
    message: 'materialSearchType 必须是 MaterialSearchType 枚举值'
  })
  materialSearchType: MaterialSearchType;

  @ApiProperty({ description: '材料类别Id' })
  @IsString()
  materialCategoryId: string;
}

export class InspectionMaterialDetailListResponseDto {
  @ApiProperty({ description: 'ID' })
  id: string;

  @ApiProperty({ description: '编码' })
  code: string;

  @ApiProperty({ description: '合同材料名称' })
  name: string;

  @ApiProperty({ description: '规格型号' })
  spec: string;

  @ApiProperty({ description: '计量单位' })
  unit: string;

  @ApiProperty({ description: '备注' })
  remark: string;
}

export class CreateInspectionDetailItem extends PickType(
  MaterialIncomingInspectionDetailDto,
  ['materialId', 'materialName', 'materialSpec', 'unit'] as const
) {}

export class CreateInspectionDetailDto {
  @ApiProperty({ type: [CreateInspectionDetailItem] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateInspectionDetailItem)
  data: CreateInspectionDetailItem[];
}

export class UpdateInspectionDetailDto extends PickType(
  MaterialIncomingInspectionDetailDto,
  [
    'id',
    'qualityStandard',
    'unit',
    'siteEntryQuantity',
    'actualQuantity',
    'appearanceDescription',
    'remark',
    'orderNo'
  ] as const
) {}

// #endregion 材料验收单明细

// #region 验收单附件

class InspectionAttachmentBaseDto {
  @ApiProperty({ description: '数据ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: '验收单ID' })
  @IsString()
  incomingInspectionId: string;

  @ApiProperty({ description: '文件名称' })
  @IsString()
  fileName: string;

  @ApiProperty({ description: '文件key' })
  @IsString()
  fileKey: string;

  @ApiProperty({ description: '文件大小' })
  @IsInt()
  fileSize: number;

  @ApiProperty({ description: '文件后缀' })
  @IsString()
  fileExt: string;

  @ApiProperty({ description: '文件类型' })
  @IsString()
  fileContentType: string;
}

export class QueryInspectionAttachmentDto extends PickType(
  InspectionAttachmentBaseDto,
  [
    'id',
    'incomingInspectionId',
    'fileName',
    'fileKey',
    'fileSize',
    'fileExt',
    'fileContentType'
  ]
) {}

export class CreateInspectionAttachmentDto extends PickType(
  InspectionAttachmentBaseDto,
  [
    'incomingInspectionId',
    'fileName',
    'fileKey',
    'fileSize',
    'fileExt',
    'fileContentType'
  ] as const
) {}

// #endregion 验收单附件
