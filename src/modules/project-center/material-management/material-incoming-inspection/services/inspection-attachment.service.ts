import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  CreateInspectionAttachmentDto,
  QueryInspectionAttachmentDto
} from '../material-incoming-inspection.dto';

@Injectable()
export class MaterialIncomingInspectionAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  // 添加附件
  async addInspectionAttachment(
    reqUser: IReqUser,
    data: CreateInspectionAttachmentDto
  ) {
    await this.prisma.materialIncomingInspectionAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  // 获取附件列表
  async getInspectionAttachmentList(
    reqUser: IReqUser,
    inspectionBillId: string
  ): Promise<QueryInspectionAttachmentDto[]> {
    return await this.prisma.materialIncomingInspectionAttachment.findMany({
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false,
        incomingInspectionId: inspectionBillId
      },
      select: {
        id: true,
        incomingInspectionId: true,
        fileName: true,
        fileKey: true,
        fileSize: true,
        fileExt: true,
        fileContentType: true
      }
    });
  }

  // 删除附件
  async deleteInspectionAttachment(reqUser: IReqUser, id: string) {
    await this.prisma.materialIncomingInspectionAttachment.update({
      data: {
        isDeleted: true,
        updateBy: reqUser.id,
        updateAt: new Date()
      },
      where: {
        id,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        orgId: reqUser.orgId
      }
    });
    return true;
  }
}
