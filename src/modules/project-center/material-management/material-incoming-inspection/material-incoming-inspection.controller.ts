import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { EnumValidationPipe } from '@/common/pipe/enum.validation.pipe';
import { PurchaseType } from '@/prisma/generated/enums';

import {
  CreateInspectionAttachmentDto,
  CreateInspectionDetailDto,
  InspectionBillDetailResponseDto,
  InspectionBillListResponseDto,
  InspectionMaterialDetailListResponseDto,
  InspectionTimeListResponseDto,
  MaterialCategoryListResponseDto,
  QueryInspectionAttachmentDto,
  QueryInspectionBillListDto,
  QueryMaterialCategoryListDto,
  QueryMaterialDetailListDto,
  SupplierAndContractResponseDto,
  UpdateInspectionBillListDto,
  UpdateInspectionDetailDto
} from './material-incoming-inspection.dto';
import { MaterialIncomingInspectionAttachmentService } from './services/inspection-attachment.service';
import { MaterialIncomingInspectionListService } from './services/inspection-bill.service';
import { MaterialIncomingInspectionDetailService } from './services/inspection-detail.service';

@ApiTags('材料进场验收单')
@Controller('material-incoming-inspection')
export class MaterialIncomingInspectionController {
  constructor(
    private readonly inspectionBillService: MaterialIncomingInspectionListService,
    private readonly inspectionDetailService: MaterialIncomingInspectionDetailService,
    private readonly inspectionAttachmentService: MaterialIncomingInspectionAttachmentService
  ) {}

  @ApiOperation({
    summary: '获取时间筛选列表',
    description: '获取时间筛选列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取时间筛选列表',
    type: InspectionTimeListResponseDto,
    isArray: true
  })
  @Get('time-list')
  async getTimeList(@ReqUser() reqUser: IReqUser) {
    return await this.inspectionBillService.getTimeList(reqUser);
  }

  @ApiOperation({
    summary: '获取单据列表',
    description: '获取单据列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取单据列表',
    type: InspectionBillListResponseDto,
    isArray: true
  })
  @Get('inspection-bill/list')
  async getBillList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryInspectionBillListDto
  ) {
    return await this.inspectionBillService.getBillList(reqUser, query);
  }

  @ApiOperation({
    summary: '新增进场验收单据',
    description: '新增进场验收单据'
  })
  @ApiResponse({
    status: 200,
    description: '新增成功',
    type: InspectionBillListResponseDto
  })
  @Post('inspection-bill/_add')
  async addInspectionBill(@ReqUser() reqUser: IReqUser) {
    return await this.inspectionBillService.addInspectionBill(reqUser);
  }

  @ApiOperation({
    summary: '修改进场验收单据',
    description: '修改进场验收单据'
  })
  @ApiResponse({
    status: 200,
    description: '修改成功'
  })
  @Post('inspection-bill/_edit')
  async editInspectionBill(
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateInspectionBillListDto
  ) {
    return await this.inspectionBillService.editInspectionBill(reqUser, data);
  }

  @ApiOperation({
    summary: '删除进场验收单据',
    description: '删除进场验收单据'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @Delete('inspection-bill/:id')
  async deleteInspectionBill(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.inspectionBillService.deleteInspectionBill(reqUser, id);
  }

  @ApiOperation({
    summary: '获取供应商和合同列表',
    description: '获取供应商和合同列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取供应商和合同列表',
    type: SupplierAndContractResponseDto,
    isArray: true
  })
  @Get('supplier-and-contract/list')
  async getSupplierAndContractList(
    @Req() req: Request,
    @ReqUser() reqUser: IReqUser,
    @Query('purchaseType', new EnumValidationPipe(PurchaseType))
    purchaseType: PurchaseType
  ) {
    return await this.inspectionDetailService.getSupplierAndContractList(
      req,
      reqUser,
      purchaseType
    );
  }

  @ApiOperation({
    summary: '获取验收单明细列表',
    description: '获取验收单明细列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取验收单明细列表',
    type: InspectionBillDetailResponseDto,
    isArray: true
  })
  @Get(':inspectionBillId/detail/list')
  async getInspectionDetailList(
    @ReqUser() reqUser: IReqUser,
    @Param('inspectionBillId') inspectionBillId: string
  ) {
    return await this.inspectionDetailService.getInspectionDetailList(
      reqUser,
      inspectionBillId
    );
  }

  @ApiOperation({
    summary: '获取可选择的材料字典分类',
    description: '获取可选择的材料字典分类'
  })
  @ApiResponse({
    status: 200,
    description: '获取可选择的材料字典分类',
    type: MaterialCategoryListResponseDto,
    isArray: true
  })
  @Get(':inspectionBillId/detail/material-category/list')
  async getInspectionMaterialCategoryList(
    @ReqUser() reqUser: IReqUser,
    @Param('inspectionBillId') inspectionBillId: string,
    @Query() query: QueryMaterialCategoryListDto
  ) {
    return await this.inspectionDetailService.getInspectionMaterialCategoryList(
      reqUser,
      inspectionBillId,
      query
    );
  }

  @ApiOperation({
    summary: '获取可选择的材料明细',
    description: '获取可选择的材料明细'
  })
  @ApiResponse({
    status: 200,
    description: '获取可选择的材料明细',
    type: InspectionMaterialDetailListResponseDto,
    isArray: true
  })
  @Get(':inspectionBillId/detail/material-detail/list')
  async getInspectionMaterialDetailList(
    @ReqUser() reqUser: IReqUser,
    @Param('inspectionBillId') inspectionBillId: string,
    @Query() query: QueryMaterialDetailListDto
  ) {
    return await this.inspectionDetailService.getInspectionMaterialDetailList(
      reqUser,
      inspectionBillId,
      query
    );
  }

  @ApiOperation({
    summary: '新增验收单明细',
    description: '新增验收单明细'
  })
  @ApiResponse({ status: 200, description: '新增成功' })
  @Post(':inspectionBillId/detail/_add')
  async addInspectionDetails(
    @ReqUser() reqUser: IReqUser,
    @Param('inspectionBillId') inspectionBillId: string,
    @Body() body: CreateInspectionDetailDto
  ) {
    return await this.inspectionDetailService.addInspectionDetails(
      reqUser,
      inspectionBillId,
      body.data
    );
  }

  @ApiOperation({
    summary: '删除验收单明细',
    description: '删除验收单明细'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @Delete('inspection-detail/:id')
  async deleteInspectionDetail(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.inspectionDetailService.deleteInspectionDetail(
      reqUser,
      id
    );
  }

  @ApiOperation({
    summary: '编辑验收单明细',
    description: '编辑验收单明细'
  })
  @ApiResponse({ status: 200, description: '编辑成功' })
  @Post('inspection-detail/_edit')
  async editInspectionDetail(
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateInspectionDetailDto
  ) {
    return await this.inspectionDetailService.editInspectionDetail(
      reqUser,
      data
    );
  }

  @ApiOperation({
    summary: '验收单附件上传',
    description: '验收单附件上传'
  })
  @ApiResponse({ status: 200, description: '上传成功' })
  @Post('inspection-bill/attachment')
  async addInspectionAttachment(
    @ReqUser() reqUser: IReqUser,
    @Body() data: CreateInspectionAttachmentDto
  ) {
    return await this.inspectionAttachmentService.addInspectionAttachment(
      reqUser,
      data
    );
  }

  @ApiOperation({
    summary: '获取验收单附件列表',
    description: '获取验收单附件列表'
  })
  @ApiResponse({
    status: 200,
    type: QueryInspectionAttachmentDto,
    isArray: true
  })
  @Get(':inspectionBillId/attachment/list')
  async getInspectionAttachmentList(
    @ReqUser() reqUser: IReqUser,
    @Param('inspectionBillId') inspectionBillId: string
  ) {
    return await this.inspectionAttachmentService.getInspectionAttachmentList(
      reqUser,
      inspectionBillId
    );
  }

  @ApiOperation({
    summary: '验收单附件删除',
    description: '验收单附件删除'
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  @Delete('inspection-bill/attachment/:id')
  async deleteInspectionAttachment(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.inspectionAttachmentService.deleteInspectionAttachment(
      reqUser,
      id
    );
  }
}
