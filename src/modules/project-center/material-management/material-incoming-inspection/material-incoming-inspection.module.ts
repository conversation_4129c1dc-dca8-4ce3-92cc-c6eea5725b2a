import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialIncomingInspectionController } from './material-incoming-inspection.controller';
import { MaterialIncomingInspectionRepository } from './repositories/inspection-bill.repositories';
import { MaterialIncomingInspectionDetailRepository } from './repositories/inspection-detail.repositories';
import { MaterialIncomingInspectionAttachmentService } from './services/inspection-attachment.service';
import { MaterialIncomingInspectionListService } from './services/inspection-bill.service';
import { MaterialIncomingInspectionDetailService } from './services/inspection-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [MaterialIncomingInspectionController],
  providers: [
    MaterialIncomingInspectionListService,
    MaterialIncomingInspectionDetailService,
    MaterialIncomingInspectionAttachmentService,
    MaterialIncomingInspectionRepository,
    MaterialIncomingInspectionDetailRepository
  ]
})
export class materialIncomingInspectionModule {}
