import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma } from '@/prisma/generated';

import {
  InspectionBillListResponseDto,
  QueryInspectionBillListDto
} from '../material-incoming-inspection.dto';

@Injectable()
export class MaterialIncomingInspectionRepository {
  constructor(private readonly prisma: PrismaService) {}

  async selectBillList(
    reqUser: IReqUser,
    query: QueryInspectionBillListDto
  ): Promise<InspectionBillListResponseDto[]> {
    const { onlyViewSelf = false } = query;

    const inspectionBills: InspectionBillListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_material_categories as (
        select
          miid.incoming_inspection_id,
          STRING_AGG(distinct miid.material_category_name, ',') material_categories
        from material_incoming_inspection_detail miid
        where miid.is_deleted = false
          and miid.tenant_id = ${reqUser.tenantId}
          and miid.org_id = ${reqUser.orgId}
        group by miid.incoming_inspection_id
      )
      select
        mii.material_receipt_status
        ,mii.id
        ,mii.purchase_type
        ,mii.supplier_id
        ,mii.supplier_name
        ,mii.contract_id
        ,mii.contract_name
        ,tmc.material_categories
        ,mii.creator
        ,mii.year
        ,mii.month
        ,mii.day
        ,mii.submit_status
        ,mii.audit_status
        ,tmc.material_categories
      from material_incoming_inspection mii
      left join temp_material_categories tmc
        on tmc.incoming_inspection_id = mii.id
      where mii.tenant_id = ${reqUser.tenantId}
        and mii.org_id = ${reqUser.orgId}
        and mii.is_deleted = false
        ${onlyViewSelf ? Prisma.sql`and mii.create_By = ${reqUser.id}` : Prisma.empty}
        ${query.year ? Prisma.sql`and mii.year = ${query.year}` : Prisma.empty}
        ${query.month ? Prisma.sql`and mii.month = ${query.month}` : Prisma.empty}
        ${query.day ? Prisma.sql`and mii.day = ${query.day}` : Prisma.empty}
      order by mii.code desc, mii.id desc
    `;

    return inspectionBills;
  }
}
