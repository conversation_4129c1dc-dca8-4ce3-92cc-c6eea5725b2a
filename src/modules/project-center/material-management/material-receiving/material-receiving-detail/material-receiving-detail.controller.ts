import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { EditMoveDto } from '@/modules/enterprise-center/enterprise-standard/business-base-info/business-base-info/business-base-info.dto';

import {
  MaterialReceivingDetailCreateDto,
  MaterialReceivingDetailResDto,
  MaterialReceivingDetailUpdateDto,
  MaterialReceivingIncomingDetailResDto
} from './material-receiving-detail.dto';
import { MaterialReceivingDetailService } from './material-receiving-detail.service';

@ApiTags('/收料单/明细')
@Controller('material-receiving-detail')
export class MaterialReceivingDetailController {
  constructor(private readonly service: MaterialReceivingDetailService) {}

  @ApiOperation({ summary: '查询可选的进场验收单' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的进场验收单成功',
    type: MaterialReceivingDetailResDto,
    isArray: true
  })
  @Get('/choose/incoming-bills/:receivingId')
  async getChooseIncomingBills(
    @Param('receivingId') receivingId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getChooseIncomingBills(receivingId, reqUser);
  }

  @ApiOperation({ summary: '查询要选择进场验收单明细（过滤已被选的）' })
  @ApiResponse({
    status: 200,
    description: '获取查询要选择进场验收单明细成功',
    type: MaterialReceivingDetailResDto,
    isArray: true
  })
  @Get('/choose/incoming-details/:incomingInspectionId')
  async getChooseIncomingBillsDetail(
    @Param('incomingInspectionId') incomingInspectionId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getChooseIncomingBillsDetail(
      incomingInspectionId,
      reqUser
    );
  }

  @ApiOperation({ summary: '查询收料单明细' })
  @ApiResponse({
    status: 200,
    description: '获取查询收料单明细成功',
    type: MaterialReceivingDetailResDto,
    isArray: true
  })
  @Get('/:receivingId')
  async getList(
    @Param('receivingId') receivingId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(receivingId, reqUser);
  }

  @ApiOperation({ summary: '新增收料单明细' })
  @ApiResponse({
    status: 200,
    description: '新增收料单明细成功'
  })
  @Post('/:receivingId')
  async add(
    @Param('receivingId') receivingId: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReceivingDetailCreateDto
  ): Promise<boolean> {
    return await this.service.add(receivingId, reqUser, data);
  }

  @ApiOperation({ summary: '编辑收料单明细' })
  @ApiResponse({
    status: 200,
    description: '编辑收料单明细成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReceivingDetailUpdateDto
  ): Promise<boolean> {
    return await this.service.update(id, reqUser, data);
  }

  @ApiOperation({ summary: '删除收料单明细' })
  @ApiResponse({
    status: 200,
    description: '删除收料单明细成功'
  })
  @Delete('/:id')
  async delete(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser
  ): Promise<boolean> {
    return await this.service.delete(id, reqUser);
  }

  @ApiOperation({
    summary: '收料单材料上下移',
    description: '收料单材料上下移'
  })
  @ApiResponse({
    status: 200,
    description: '收料单材料上下移成功'
  })
  @Post('/move')
  async move(@ReqUser() reqUser: IReqUser, @Body() data: EditMoveDto) {
    const { moveType, id } = data;
    if (moveType === 'up') {
      // 上移
      await this.service.up(id, reqUser);
    } else {
      // 下移
      await this.service.down(id, reqUser);
    }
    return true;
  }

  @ApiOperation({
    summary: '获取数据追溯记录',
    description: '获取数据追溯记录'
  })
  @ApiResponse({
    status: 200,
    description: '获取数据追溯记录成功',
    type: MaterialReceivingIncomingDetailResDto,
    isArray: true
  })
  @Get('/traceability-record/:id')
  async getTraceabilityRecord(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.service.getTraceabilityRecord(reqUser, id);
  }

  @ApiOperation({
    summary: '删除数据追溯记录',
    description: '删除数据追溯记录'
  })
  @ApiResponse({
    status: 200,
    description: '删除数据追溯记录成功'
  })
  @Delete('/traceability-record/:id')
  async deleteTraceabilityRecord(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ) {
    return await this.service.deleteTraceabilityRecord(reqUser, id);
  }
}
