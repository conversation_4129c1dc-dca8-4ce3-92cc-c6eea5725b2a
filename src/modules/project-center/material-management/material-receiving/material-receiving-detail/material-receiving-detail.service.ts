import { BadRequestException, Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as uuid from 'uuid';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma, PurchaseType } from '@/prisma/generated';

import {
  MaterialReceivingDetailCreateDto,
  MaterialReceivingDetailCreateListDto,
  MaterialReceivingDetailCreateOrUpdateListDto,
  MaterialReceivingDetailUpdateDto
} from './material-receiving-detail.dto';

@Injectable()
export class MaterialReceivingDetailService {
  constructor(private readonly prisma: PrismaService) {}

  // 查询收料单下的明细
  async getList(receivingId: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    const list = await this.prisma.materialReceivingDetail.findMany({
      where: {
        receivingId,
        orgId,
        tenantId,
        isDeleted: false
      }
    });
    return list;
    // return list.map((item) => ({
    //   ...item,
    //   actualQuantity: item.actualQuantity?.toNumber() ?? null,
    //   priceExcludingTax: item.priceExcludingTax?.toNumber() ?? null,
    //   priceIncludingTax: item.priceIncludingTax?.toNumber() ?? null,
    //   taxExcludedAmount: item.taxExcludedAmount?.toNumber() ?? null,
    //   taxIncludedAmount: item.taxIncludedAmount?.toNumber() ?? null
  }

  // 查询可选的进场验收单
  async getChooseIncomingBills(receivingId: string, reqUser: IReqUser) {
    // 查询收料单绑定的供应商id
    const { tenantId, orgId } = reqUser;
    const receiving = await this.getReceiving(receivingId, reqUser);
    return await this.prisma.$queryRaw<any[]>`
       select
        mii.id
        ,mii.code
        ,mii.purchase_type
        ,mii.supplier_name
        ,mii.contract_name
        ,mii.creator
        ,mii.create_at
        ,mii.submit_status
        ,mii.audit_status
      from material_incoming_inspection mii
      where mii.org_id = ${orgId}
        and mii.tenant_id = ${tenantId}
        and mii.supplier_id = ${receiving.supplierId}
        and mii.is_deleted = false
        and EXISTS (
          select 
            1 
          from material_incoming_inspection_detail miid
          where miid.org_id = ${orgId}
            and miid.tenant_id = ${tenantId}
            and miid.is_deleted = false
            and miid.incoming_inspection_id = mii.id
            and miid.id not in (
              select 
                mrid.incoming_inspection_detail_id
              from material_receiving_incoming_detail mrid
              where mrid.org_id = ${orgId}
                and mrid.tenant_id = ${tenantId}
                and mrid.is_deleted = false
            )
        )
        order by mii.create_at desc
    `;
  }

  /**
   * 根据验收单查询验收单的明细（过滤已被选的）
   * @param incomingInspectionId
   * @param reqUser
   * @returns
   */
  async getChooseIncomingBillsDetail(
    incomingInspectionId: string,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId } = reqUser;
    // 查询进场验收单的类型
    const materialIncomingInspection =
      await this.prisma.materialIncomingInspection.findUnique({
        select: {
          purchaseType: true
        },
        where: {
          id: incomingInspectionId,
          orgId,
          tenantId,
          isDeleted: false
        }
      });
    if (!materialIncomingInspection) {
      throw new BadRequestException('无此验收单');
    }
    // 有合同的采购类型
    const purchaseTypeList = [
      'SELF_PURCHASE' as PurchaseType,
      'CENTRALIZED_PURCHASE' as PurchaseType,
      'PARTY_A_DIRECTED' as PurchaseType
    ];
    if (purchaseTypeList.includes(materialIncomingInspection.purchaseType)) {
      // 需要查询合同单价用于计算
      return await this.prisma.$queryRaw`
      select 
        miid.id
        , miid.incoming_inspection_id
        , miid.material_id
        , miid.material_name
        , miid.material_spec
        , miid.material_category_name
        , miid.quality_standard
        , miid.unit as incoming_unit
				, ccmd.unit as contract_unit
        , miid.actual_quantity
        , ccmd.change_price_including_tax as price_including_tax
        , ccmd.change_price_excluding_tax as price_excluding_tax
        , mc.price_type
        , ccmd.remark
      from material_incoming_inspection_detail miid
      join material_incoming_inspection mii
      on mii.id = miid.incoming_inspection_id
        and mii.is_deleted = false
        and mii.org_id = miid.org_id
        and mii.tenant_id = miid.tenant_id
        and mii.is_deleted = false
      join material_contract mc
      on mc.id = mii.contract_id
        and mc.is_deleted = false
        and mc.org_id = miid.org_id
        and mc.tenant_id = miid.tenant_id
      join contract_consume_material_details ccmd
      on ccmd.material_contract_id = mii.contract_id
        and ccmd.material_dictionary_detail_id = miid.material_id
        and ccmd.org_id = miid.org_id
        and ccmd.tenant_id = miid.tenant_id
        and ccmd.is_deleted = false
      where miid.is_deleted = false
        and miid.org_id = ${orgId}
        and miid.tenant_id = ${tenantId}
        and miid.incoming_inspection_id = ${incomingInspectionId}
        and NOT EXISTS (
          select 
            1
          from material_receiving_incoming_detail mrid
          where mrid.is_deleted = false
            and mrid.org_id = ${orgId}
            and mrid.tenant_id = ${tenantId}
            and miid.id = mrid.incoming_inspection_detail_id
      )
      order by miid.order_no asc
    `;
    } else {
      // 甲供、调入无单价
      return await this.prisma.$queryRaw`
      select 
        miid.id
        , miid.incoming_inspection_id
        , miid.material_id
        , miid.material_name
        , miid.material_spec
        , miid.material_category_name
        , miid.quality_standard
        , miid.unit as incoming_unit
				, null as contract_unit
        , miid.actual_quantity
        , null as price_including_tax
        , null as price_excluding_tax
        , null as price_type
        , miid.remark
      from material_incoming_inspection_detail miid
      join material_incoming_inspection mii
      on mii.id = miid.incoming_inspection_id
        and mii.is_deleted = false
        and mii.org_id = miid.org_id
        and mii.tenant_id = miid.tenant_id
        and mii.is_deleted = false
      where miid.is_deleted = false
        and miid.org_id = ${orgId}
        and miid.tenant_id = ${tenantId}
        and miid.incoming_inspection_id = ${incomingInspectionId}
        and NOT EXISTS (
          select 
            1
          from material_receiving_incoming_detail mrid
          where mrid.is_deleted = false
            and mrid.org_id = ${orgId}
            and mrid.tenant_id = ${tenantId}
            and miid.id = mrid.incoming_inspection_detail_id
      )
      order by miid.order_no asc
    `;
    }
  }

  /**
   * 查询收料单
   * @param receivingId
   * @param reqUser
   */
  async getReceiving(receivingId: string, reqUser: IReqUser) {
    const receiving = await this.prisma.materialReceiving.findUnique({
      where: {
        id: receivingId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    if (!receiving) {
      throw new BadRequestException('收料单不存在');
    }
    return receiving;
  }

  async add(
    receivingId: string,
    reqUser: IReqUser,
    data: MaterialReceivingDetailCreateDto
  ) {
    const { list } = data;
    // 查询收料单的单据类型
    const materialReceiving = await this.getReceiving(receivingId, reqUser);
    // 有合同的采购类型
    const hasContractPurchaseType: PurchaseType[] = [
      PurchaseType.SELF_PURCHASE,
      PurchaseType.CENTRALIZED_PURCHASE,
      PurchaseType.PARTY_A_DIRECTED
    ];
    const isExit = hasContractPurchaseType.includes(
      materialReceiving.purchaseType as PurchaseType
    );
    if (isExit) {
      // 需要进行数据合并
      await this.mergeMaterialsAdd(
        receivingId,
        list,
        reqUser,
        materialReceiving.contractId as string
      );
    } else {
      // 甲供、调入不需要合并
      await this.notMergeMaterialsAdd(receivingId, list, reqUser);
    }
    return true;
  }

  async mergeMaterialsAdd(
    receivingId: string,
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser,
    contractId: string
  ) {
    const { orgId, tenantId, id: userId } = reqUser;
    // 同材料合并
    const mergeList: MaterialReceivingDetailCreateOrUpdateListDto[] =
      await this.mergeMaterials(receivingId, list, reqUser, contractId);
    const addList = mergeList.filter((item) => item.addOrUpdate === 'add');
    const updateList = mergeList.filter(
      (item) => item.addOrUpdate === 'update'
    );
    // 获取合同的发票类型
    const invoiceType = await this.getInvoiceType(contractId, reqUser);
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceivingDetail.createMany({
        data: addList.map((item) => {
          const {
            materialIncomingInspectionDetailId,
            materialReceivingDetailId,
            addOrUpdate,
            incomingUnit,
            contractUnit,
            ...reset
          } = item;
          return {
            ...reset,
            unit: item.contractUnit,
            receivingId,
            orgId,
            tenantId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
      await tx.materialReceivingIncomingDetail.createMany({
        data: list.map((item) => {
          return {
            materialIncomingInspectionDetailId:
              item.materialIncomingInspectionDetailId,
            materialReceivingDetailId: item.materialReceivingDetailId as string,
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
      await Promise.all(
        updateList.map(async (item: any) => {
          await tx.materialReceivingDetail.update({
            where: {
              id: item.id
            },
            data: {
              actualQuantity: item.actualQuantity,
              priceExcludingTax: item.priceExcludingTax,
              priceIncludingTax: item.priceIncludingTax,
              orgId,
              tenantId,
              updateBy: userId
            }
          });
        })
      );
      await tx.materialReceivingInventory.createMany({
        data: addList.map((item) => {
          return {
            materialId: item.materialId,
            materialName: item.materialName,
            materialSpec: item.materialSpec,
            price:
              invoiceType === '增值税专用发票'
                ? Decimal(item.priceExcludingTax ?? 0)
                : Decimal(item.priceIncludingTax ?? 0),
            inventoryQuantity: Decimal(item.actualQuantity ?? 0),
            receivingQuantity: Decimal(item.actualQuantity ?? 0),
            requisitionQuantity: 0,
            receivingId,
            orgId,
            tenantId,
            createBy: userId
          };
        })
      });
      await Promise.all(
        updateList.map(async (item: any) => {
          await tx.materialReceivingInventory.updateMany({
            where: {
              receivingId,
              materialId: item.materialId
            },
            data: {
              inventoryQuantity: Decimal(item.actualQuantity ?? 0),
              receivingQuantity: Decimal(item.actualQuantity ?? 0),
              orgId,
              tenantId,
              updateBy: userId
            }
          });
        })
      );
    });
  }

  // 获取合同的发票类型
  async getInvoiceType(contractId: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 查询合同信息
    const contract = await this.prisma.materialContract.findUnique({
      where: {
        id: contractId,
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        contractTemplateId: true
      }
    });
    // 获取发票类型的字段
    const field = await this.prisma.$queryRaw<any[]>`
      select mcfr.value from material_contract_field_rule mcfr
        join contract_template_field_rule ctfr
        on ctfr.id = mcfr.contract_template_field_rule_id
          and ctfr.contract_template_id = mcfr.contract_template_id
          and ctfr.is_deleted = false
        join field_rule fr
        on fr.code = '发票类型' and fr.id = ctfr.field_rule_id and fr.is_deleted = false
        where mcfr.contract_template_id = ${contract?.contractTemplateId}
          and mcfr.material_contract_id = ${contractId}
          and mcfr.org_id  = ${reqUser.orgId}
          and mcfr.tenant_id = ${reqUser.tenantId}
          and mcfr.is_deleted = false
    `;
    if (!field[0]?.value) {
      throw new BadRequestException('请先设置发票类型');
    }
    return field[0]?.value;
  }

  async notMergeMaterialsAdd(
    receivingId: string,
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser
  ) {
    const { orgId, tenantId, id: userId } = reqUser;
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceivingDetail.createMany({
        data: list.map((item) => {
          const {
            materialIncomingInspectionDetailId,
            materialReceivingDetailId,
            ...reset
          } = item;
          return {
            ...reset,
            unit: item.incomingUnit,
            receivingId,
            orgId,
            tenantId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
      await tx.materialReceivingIncomingDetail.createMany({
        data: list.map((item) => {
          return {
            materialIncomingInspectionDetailId:
              item.materialIncomingInspectionDetailId,
            materialReceivingDetailId: item.materialReceivingDetailId as string,
            tenantId,
            orgId,
            createBy: userId,
            updateBy: userId
          };
        })
      });
    });
  }

  // 数据合并
  async mergeMaterials(
    receivingId: string,
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser,
    contractId: string
  ): Promise<MaterialReceivingDetailCreateOrUpdateListDto[]> {
    // 同一材料名称加规格进行数据合并（单价类型为固定单价）
    const { orgId, tenantId } = reqUser;
    const mergedMap = new Map<
      string,
      MaterialReceivingDetailCreateOrUpdateListDto
    >();
    // 查询已有的数据列表
    const details = await this.prisma.materialReceivingDetail.findMany({
      where: {
        receivingId,
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        receivingId: true,
        unit: true,
        materialName: true,
        materialSpec: true,
        priceType: true,
        actualQuantity: true,
        priceExcludingTax: true,
        priceIncludingTax: true
      }
    });
    list = await this.convertActualQuantity(list, reqUser, contractId);
    for (const item of list) {
      const materialReceivingDetailId = uuid.v7();
      item.materialReceivingDetailId = materialReceivingDetailId;
      const key = `${item.materialName}${item.materialSpec}${item.contractUnit}${item.priceExcludingTax}`;
      if (item.priceType === '固定单价') {
        // 查询数据库是否存在
        const detail = details.find(
          (detail) =>
            detail.materialName === item.materialName &&
            detail.materialSpec === item.materialSpec &&
            detail.unit === item.contractUnit &&
            detail.priceExcludingTax === item.priceExcludingTax &&
            detail.receivingId === receivingId
        );
        // 查询本次提交是否存在
        const existing = mergedMap.get(key);
        if (detail) {
          item.materialReceivingDetailId = detail.id;
          if (!existing) {
            mergedMap.set(key, {
              id: detail.id,
              ...item,
              actualQuantity: Prisma.Decimal(detail.actualQuantity || 0)
                .add(item.actualQuantity || 0)
                .toNumber(),
              priceExcludingTax: null,
              priceIncludingTax: null,
              addOrUpdate: 'update'
            });
          } else {
            existing.actualQuantity =
              (existing.actualQuantity || 0) + (item.actualQuantity || 0);
          }
        } else {
          // 查询本次提交是否存在
          const existing = mergedMap.get(key);
          if (existing) {
            // 累加实际数量（处理可能的null/undefined）
            existing.actualQuantity =
              (existing.actualQuantity || 0) + (item.actualQuantity || 0);
          } else {
            // 深度拷贝避免原始数据引用问题
            mergedMap.set(key, {
              ...item,
              id: materialReceivingDetailId,
              addOrUpdate: 'add'
            });
          }
        }
      } else {
        // 如果非固定单价，则直接添加
        mergedMap.set(key, {
          ...item,
          id: materialReceivingDetailId,
          addOrUpdate: 'add'
        });
      }
    }

    return Array.from(mergedMap.values());
  }

  // 根据合同的单位和验收单的单位换算实收数量
  async convertActualQuantity(
    list: MaterialReceivingDetailCreateListDto[],
    reqUser: IReqUser,
    contractId: string
  ): Promise<MaterialReceivingDetailCreateListDto[]> {
    const { orgId, tenantId } = reqUser;
    const materialIds = list.map((item) => item.materialId);
    // 查询合同的单位换算
    const contractUnitList =
      await this.prisma.materialContractUnitCalculation.findMany({
        where: {
          materialContractId: contractId,
          materialDetailId: {
            in: materialIds
          },
          orgId,
          tenantId,
          isDeleted: false
        },
        select: {
          unit: true,
          factor: true,
          materialDetailId: true
        }
      });
    return list.map((item) => {
      // 若验收单单位与合同单位不一致，则进行单位换算
      if (item.contractUnit !== item.incomingUnit) {
        // 查找该材料合同单位换算
        const obj = contractUnitList.find(
          (unit) =>
            unit.materialDetailId === item.materialId &&
            unit.unit === item.incomingUnit
        );
        item.actualQuantity = Prisma.Decimal(item.actualQuantity || 0)
          .mul(obj?.factor || 1)
          .toNumber();
      }
      return item;
    });
  }

  // 编辑明细
  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialReceivingDetailUpdateDto
  ) {
    await this.prisma.materialReceivingDetail.updateMany({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  // 删除明细
  async delete(id: string, reqUser: IReqUser) {
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceivingDetail.update({
        where: {
          id,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      await tx.materialReceivingIncomingDetail.updateMany({
        where: {
          materialReceivingDetailId: id,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });
    return true;
  }

  // 上移
  async up(id: string, reqUser: IReqUser) {
    // 查询当前明细的位置
    const { tenantId, orgId } = reqUser;
    // 物资采购
    const currentData = await this.prisma.materialReceivingDetail.findFirst({
      select: {
        id: true,
        orderNo: true
      },
      where: {
        id,
        tenantId: tenantId,
        orgId: orgId,
        isDeleted: false
      }
    });
    // 查询除本身之外的最大的排序
    const maxSortData = await this.prisma.materialReceivingDetail.findFirst({
      select: {
        id: true,
        orderNo: true
      },
      where: {
        orderNo: {
          lt: currentData?.orderNo
        },
        tenantId: tenantId,
        orgId: orgId,
        isDeleted: false
      },
      orderBy: {
        orderNo: 'desc'
      }
    });
    await this.prisma.$transaction(async (txPrisma) => {
      await txPrisma.materialReceivingDetail.update({
        where: {
          id,
          tenantId,
          orgId
        },
        data: {
          orderNo: maxSortData?.orderNo,
          updateBy: reqUser.id
        }
      });
      await txPrisma.materialReceivingDetail.update({
        where: {
          id: maxSortData?.id,
          tenantId,
          orgId
        },
        data: {
          orderNo: currentData?.orderNo,
          updateBy: reqUser.id
        }
      });
    });
    return true;
  }

  // 下移
  async down(id: string, reqUser: IReqUser) {
    // 查询当前明细的位置
    const { tenantId, orgId } = reqUser;
    // 物资采购
    const currentData = await this.prisma.materialReceivingDetail.findFirst({
      select: {
        id: true,
        orderNo: true
      },
      where: {
        id,
        tenantId: tenantId,
        orgId: orgId,
        isDeleted: false
      }
    });
    // 查询除本身之外的最小的排序
    const maxSortData = await this.prisma.materialReceivingDetail.findFirst({
      select: {
        id: true,
        orderNo: true
      },
      where: {
        orderNo: {
          gt: currentData?.orderNo
        },
        tenantId: tenantId,
        orgId: orgId,
        isDeleted: false
      },
      orderBy: {
        orderNo: 'asc'
      }
    });
    await this.prisma.$transaction(async (txPrisma) => {
      await txPrisma.materialReceivingDetail.update({
        where: {
          id,
          tenantId,
          orgId
        },
        data: {
          orderNo: maxSortData?.orderNo,
          updateBy: reqUser.id
        }
      });
      await txPrisma.materialReceivingDetail.update({
        where: {
          id: maxSortData?.id,
          tenantId,
          orgId
        },
        data: {
          orderNo: currentData?.orderNo,
          updateBy: reqUser.id
        }
      });
    });
    return true;
  }

  async getTraceabilityRecord(reqUser: IReqUser, id: string) {
    const { tenantId, orgId } = reqUser;
    return await this.prisma.$queryRaw<any[]>`
      select 
         mrid.id
        , mii.code
        , miid.site_entry_quantity
        , miid.actual_quantity
        , miid.unit 
      from material_receiving_incoming_detail mrid
      join material_incoming_inspection_detail miid
        on miid.id = mrid.incoming_inspection_detail_id
        and miid.is_deleted = false
        and miid.org_id = mrid.org_id
        and miid.tenant_id = mrid.tenant_id
      join material_incoming_inspection mii
        on mii.id = miid.incoming_inspection_id
        and mii.is_deleted = false
        and mii.org_id = miid.org_id
        and mii.tenant_id = miid.tenant_id
      where mrid.receiving_detail_id = ${id}
        and mrid.is_deleted = false
        and mrid.org_id = ${orgId}
        and mrid.tenant_id = ${tenantId}
    `;
  }

  async deleteTraceabilityRecord(reqUser: IReqUser, id: string) {
    const { tenantId, orgId } = reqUser;
    // 查询该数据对应的收料明细
    const data = await this.prisma.materialReceivingIncomingDetail.findFirst({
      select: {
        id: true,
        materialReceivingDetailId: true,
        materialIncomingInspectionDetailId: true
      },
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      }
    });
    // 查询该数据对应的验收单明细
    const incomingInspectionDetail =
      await this.prisma.materialIncomingInspectionDetail.findFirst({
        select: {
          id: true,
          actualQuantity: true,
          materialName: true,
          materialSpec: true,
          unit: true
        },
        where: {
          id: data?.materialIncomingInspectionDetailId,
          orgId,
          tenantId,
          isDeleted: false
        }
      });
    // 查询该数据对应的收料单明细
    const materialReceivingDetail =
      await this.prisma.materialReceivingDetail.findUnique({
        select: {
          id: true,
          actualQuantity: true,
          materialName: true,
          materialSpec: true,
          unit: true
        },
        where: {
          id: data?.materialReceivingDetailId,
          orgId,
          tenantId,
          isDeleted: false
        }
      });
    if (
      materialReceivingDetail?.actualQuantity ===
      incomingInspectionDetail?.actualQuantity
    ) {
      await this.prisma.$transaction(async (tx) => {
        await tx.materialReceivingDetail.update({
          data: {
            isDeleted: true
          },
          where: {
            id: data?.materialReceivingDetailId,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
        await tx.materialReceivingIncomingDetail.update({
          data: {
            isDeleted: true
          },
          where: {
            id,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
      });
    }
    if (
      materialReceivingDetail?.actualQuantity !==
      incomingInspectionDetail?.actualQuantity
    ) {
      await this.prisma.$transaction(async (tx) => {
        await tx.materialReceivingDetail.update({
          data: {
            actualQuantity: Prisma.Decimal(
              materialReceivingDetail?.actualQuantity || 0
            )
              .sub(incomingInspectionDetail?.actualQuantity || 0)
              .toNumber(),
            updateBy: reqUser.id,
            taxExcludedAmount: null,
            taxIncludedAmount: null
          },
          where: {
            id: data?.materialReceivingDetailId,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
        await tx.materialReceivingIncomingDetail.update({
          data: {
            isDeleted: true
          },
          where: {
            id,
            isDeleted: false,
            tenantId,
            orgId
          }
        });
      });
    }
    return true;
  }
}
