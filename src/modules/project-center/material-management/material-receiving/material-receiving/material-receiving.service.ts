import { BadRequestException, Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { AuditStatus, SubmitStatus } from '@/prisma/generated';

import { MaterialReceivingDetailService } from '../material-receiving-detail/material-receiving-detail.service';
import {
  MaterialReceivingDateTreeResDto,
  MaterialReceivingUpdateDto,
  QueryIncomingContractDto,
  QueryIncomingSupplierDto,
  QueryMaterialReceivingDto
} from './material-receiving.dto';

@Injectable()
export class MaterialReceivingService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly platformService: PlatformService,
    private readonly materialReceivingDetailService: MaterialReceivingDetailService
  ) {}

  /**
   * 查询进场验收单下的合同
   * @param reqUser
   * @param query
   * @returns
   */
  async getContractList(reqUser: IReqUser, query: QueryIncomingContractDto) {
    const { tenantId, orgId } = reqUser;
    const { supplierId } = query;
    return await this.prisma.materialIncomingInspection.findMany({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        submitStatus: 'SUBMITTED',
        auditStatus: 'APPROVED',
        supplierId: supplierId ? supplierId : undefined
      },
      distinct: ['id', 'contractId'],
      select: {
        id: true,
        purchaseType: true,
        supplierId: true,
        supplierName: true,
        contractId: true,
        contractName: true
      }
    });
  }

  /**
   * 查询进场验收单下的供应商
   * @param reqUser
   * @param query
   * @returns
   */
  async getSupplierList(reqUser: IReqUser, query: QueryIncomingSupplierDto) {
    const { tenantId, orgId } = reqUser;
    const { contractId } = query;
    return await this.prisma.materialIncomingInspection.findMany({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        submitStatus: 'SUBMITTED',
        auditStatus: 'APPROVED',
        contractId: contractId ? contractId : undefined
      },
      distinct: ['id', 'supplierId'],
      select: {
        id: true,
        purchaseType: true,
        supplierId: true,
        supplierName: true,
        contractId: true,
        contractName: true
      }
    });
  }

  /**
   * 新增收料单
   * @param reqUser
   * @returns
   */
  async add(req: Request, reqUser: IReqUser) {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    // 获取当前收料单的最新的收料单编号
    const code = await this.getMaxCode(reqUser, year, month);
    // 获取当前组织的名称
    const project = await this.platformService.getCurrentOrgInfo(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    await this.prisma.materialReceiving.create({
      data: {
        projectName: project.name || '',
        creator: reqUser.nickname,
        year: year,
        month: month,
        day: day,
        code: code,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  // 获取当前收料单的最新的收料单编号
  async getMaxCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const { tenantId, orgId } = reqUser;
    const code = ['收', `${year}${String(month).padStart(2, '0')}`, '001'];
    const maxCode = await this.prisma.materialReceiving.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        year,
        month
      },
      orderBy: {
        code: 'desc'
      }
    });
    if (maxCode?.code.split('-')[1] === code[1]) {
      const lastCode = maxCode.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }
    return code.join('-');
  }

  // 编辑收料单
  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialReceivingUpdateDto
  ) {
    await this.prisma.materialReceiving.update({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async getDateTree(
    reqUser: IReqUser
  ): Promise<MaterialReceivingDateTreeResDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialReceiving.findMany({
      distinct: ['year', 'month', 'day'],
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, MaterialReceivingDateTreeResDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          count: await this.getDateCount(reqUser, time.year, time.month),
          parentId: null,
          year: time.year,
          month: time.month,
          day: time.day
        };
      }

      // 添加子级 年_月_日
      resultMap[`${time.year}_${time.month}_${time.day}`] = {
        id: `${time.year}_${time.month}_${time.day}`,
        count: await this.getDateCount(
          reqUser,
          time.year,
          time.month,
          time.day
        ),
        parentId: `${time.year}_${time.month}`,
        year: time.year,
        month: time.month,
        day: time.day
      };
    }

    return Object.values(resultMap);
  }

  // 查询时间下的数据数量
  async getDateCount(
    reqUser: IReqUser,
    year: number,
    month: number,
    day?: number
  ): Promise<number> {
    return await this.prisma.materialReceiving.count({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        year: year,
        month: month,
        day: day ? day : undefined
      }
    });
  }

  /**
   * 获取收料单列表
   * @param reqUser
   * @param query
   */
  async getList(reqUser: IReqUser, query: QueryMaterialReceivingDto) {
    const res = await this.prisma.materialReceiving.findMany({
      select: {
        id: true,
        materialSettlementStatus: true,
        projectName: true,
        code: true,
        purchaseType: true,
        supplierId: true,
        supplierName: true,
        contractId: true,
        contractName: true,
        taxExcludedAmount: true,
        taxIncludedAmount: true,
        submitStatus: true,
        auditStatus: true,
        year: true,
        month: true,
        day: true,
        creator: true,
        materialReceivingDetail: {
          select: {
            materialCategoryName: true
          }
        }
      },
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        year: query.year ? query.year : undefined,
        month: query.month ? query.month : undefined,
        day: query.day ? query.day : undefined,
        materialReceivingDetail: {
          some: {
            isDeleted: false
          }
        }
      },
      orderBy: {
        createAt: 'desc'
      }
    });
    return res.map((item) => {
      // 提取所有类别名称并去重
      const categories = [
        ...new Set(
          item.materialReceivingDetail.map(
            (detail) => detail.materialCategoryName
          )
        )
      ].join(',');

      // 返回处理后的对象（移除原 detail 字段，添加合并后的字段）
      const { materialReceivingDetail, ...rest } = item;
      return {
        ...rest,
        materialCategories: categories
      };
    });
  }

  /**
   * 获取单个数据
   * @param id
   * @param reqUser
   * @returns
   */
  async getOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    const materialReceiving = await this.prisma.materialReceiving.findUnique({
      where: {
        id,
        orgId,
        tenantId
      }
    });
    if (!materialReceiving) {
      throw new BadRequestException('收料单不存在');
    }
    return materialReceiving;
  }

  /**
   * 删除收料单
   * @param id
   * @param reqUser
   */
  async delete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 删除前校验
    await this.beforeDelete(id, reqUser);
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceiving.update({
        where: {
          id,
          orgId,
          tenantId
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialReceivingDetail.updateMany({
        where: {
          receivingId: id,
          orgId,
          tenantId
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialReceivingIncomingDetail.updateMany({
        where: {
          receivingId: id,
          orgId,
          tenantId
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
    });
    return true;
  }

  /**
   * 删除前校验
   * @param id
   * @param reqUser
   */
  async beforeDelete(id: string, reqUser: IReqUser) {
    // 查询收料单
    const materialReceiving = await this.getOne(id, reqUser);
    if (materialReceiving.submitStatus !== SubmitStatus.PENDING) {
      throw new BadRequestException('已提交的收料单，不能删除');
    }
  }

  /**
   * 修改提交状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    let amountObj;
    const data: {
      submitStatus: SubmitStatus;
      updateBy: string;
      taxExcludedAmount?: Decimal | null;
      taxIncludedAmount?: Decimal | null;
    } = {
      submitStatus,
      updateBy: userId
    };
    // 提交状态变更校验逻辑
    await this.beforeUpdateSubmitStatus(id, reqUser, submitStatus);
    if (submitStatus === SubmitStatus.SUBMITTED) {
      // 提交时，统计收料明细含税金额和不含税金额
      amountObj = await this.getReceivingTaxAmount(id, reqUser);
      data.taxExcludedAmount = amountObj._sum.taxExcludedAmount || null;
      data.taxIncludedAmount = amountObj._sum.taxIncludedAmount || null;
    }
    await this.prisma.materialReceiving.update({
      where: {
        id,
        orgId,
        tenantId
      },
      data: data
    });
    return true;
  }

  async getReceivingTaxAmount(id: string, reqUser: IReqUser) {
    return await this.prisma.materialReceivingDetail.aggregate({
      where: {
        receivingId: id,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      },
      _sum: {
        taxIncludedAmount: true,
        taxExcludedAmount: true
      }
    });
  }

  /**
   * 提交状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    // 查询收料单
    const materialReceiving = await this.getOne(id, reqUser);
    if (
      submitStatus === SubmitStatus.PENDING &&
      materialReceiving.auditStatus === AuditStatus.APPROVED
    ) {
      // 已审批不可取消提交
      throw new BadRequestException('已审批的收料单，不能取消提交');
    }
    // 查询收料单是否有明细
    const detail = await this.materialReceivingDetailService.getList(
      id,
      reqUser
    );
    if (!detail.length) {
      throw new BadRequestException('收料单没有明细，不能提交');
    }
  }

  /**
   * 修改审批状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 审批状态变更校验逻辑
    await this.beforeUpdateAuditStatus(id, reqUser, auditStatus);
    await this.prisma.materialReceiving.update({
      where: {
        id,
        orgId,
        tenantId
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  async beforeUpdateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    // 查询收料单
    const materialReceiving = await this.getOne(id, reqUser);
    if (
      auditStatus === AuditStatus.PENDING &&
      materialReceiving.auditStatus === AuditStatus.APPROVED
    ) {
      // 校验是否被下级引用，后面补充
      // 被下级引用的收料单，不能撤销审批
      // throw new BadRequestException('被下级引用的收料单，不能撤销审批');
    }
  }
}
