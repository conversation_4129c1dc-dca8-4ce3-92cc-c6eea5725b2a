import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialIncomingContractResDto,
  MaterialReceivingResDto,
  MaterialReceivingUpdateAuditStatusDto,
  MaterialReceivingUpdateDto,
  MaterialReceivingUpdateSubmitStatusDto,
  QueryIncomingContractDto,
  QueryIncomingSupplierDto,
  QueryMaterialReceivingDto
} from './material-receiving.dto';
import { MaterialReceivingService } from './material-receiving.service';

@ApiTags('收料单/列表')
@Controller('material-receiving')
export class MaterialReceivingController {
  constructor(private readonly service: MaterialReceivingService) {}

  @ApiOperation({ summary: '进场验收单下所有的合同列表' })
  @ApiResponse({
    status: 200,
    description: '获取进场验收单下所有的合同列表成功',
    type: MaterialIncomingContractResDto,
    isArray: true
  })
  @Get('/contract-list')
  async getContractList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryIncomingContractDto
  ) {
    return await this.service.getContractList(reqUser, query);
  }

  @ApiOperation({ summary: '进场验收单下所有的供应商列表' })
  @ApiResponse({
    status: 200,
    description: '获取进场验收单下所有的供应商列表成功',
    type: MaterialReceivingResDto,
    isArray: true
  })
  @Get('/supplier-list')
  async getSupplierList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryIncomingSupplierDto
  ) {
    return await this.service.getSupplierList(reqUser, query);
  }

  @ApiOperation({ summary: '新增收料单' })
  @ApiResponse({
    status: 200,
    description: '新增收料单成功'
  })
  @Post()
  async add(@Req() req: Request, @ReqUser() reqUser: IReqUser) {
    return await this.service.add(req, reqUser);
  }

  @ApiOperation({ summary: '编辑收料单' })
  @ApiResponse({
    status: 200,
    description: '编辑收料单成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReceivingUpdateDto
  ) {
    return await this.service.update(id, reqUser, data);
  }

  @ApiOperation({ summary: '收料单左侧树' })
  @ApiResponse({
    status: 200,
    description: '获取收料单左侧树成功',
    type: MaterialReceivingResDto,
    isArray: true
  })
  @Get('/date-tree')
  async getDateTree(@ReqUser() reqUser: IReqUser) {
    return await this.service.getDateTree(reqUser);
  }

  @ApiOperation({ summary: '收料单/列表' })
  @ApiResponse({
    status: 200,
    description: '获取收料单/列表成功',
    type: MaterialReceivingResDto,
    isArray: true
  })
  @Get()
  async getList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryMaterialReceivingDto
  ) {
    return await this.service.getList(reqUser, query);
  }

  @ApiOperation({ summary: '删除收料单' })
  @ApiResponse({
    status: 200,
    description: '删除收料单成功',
    type: MaterialReceivingResDto,
    isArray: true
  })
  @Delete('/:id')
  async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.delete(id, reqUser);
  }

  @ApiOperation({ summary: '提交状态变更' })
  @ApiResponse({
    status: 200,
    description: '提交状态变更成功'
  })
  @Patch('/submit/:id')
  async updateSubmitStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Query() body: MaterialReceivingUpdateSubmitStatusDto
  ) {
    const { submitStatus } = body;
    return await this.service.updateSubmitStatus(id, reqUser, submitStatus);
  }

  @ApiOperation({ summary: '审核状态变更' })
  @ApiResponse({
    status: 200,
    description: '审核状态变更成功'
  })
  @Patch('/audit/:id')
  async updateAuditStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Query() body: MaterialReceivingUpdateAuditStatusDto
  ) {
    const { auditStatus } = body;
    return await this.service.updateAuditStatus(id, reqUser, auditStatus);
  }
}
