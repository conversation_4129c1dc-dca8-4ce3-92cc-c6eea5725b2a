import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { materialContractAccessoryCreateDto } from './material-contract-accessory.dto';

@Injectable()
export class MaterialContractAccessoryService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(contractId: string, reqUser: IReqUser) {
    return await this.prisma.materialContractAccessory.findMany({
      where: {
        materialContractId: contractId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  async add(data: materialContractAccessoryCreateDto, reqUser: IReqUser) {
    return await this.prisma.materialContractAccessory.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id
      }
    });
  }

  async delete(id: string, reqUser: IReqUser) {
    return await this.prisma.materialContractAccessory.update({
      where: {
        id
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
