import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsArray,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import { EditMoveDto } from '@/modules/enterprise-center/enterprise-standard/contract-template/contract-template/contract-template.dto';
import {
  AlterType,
  ContractTemplateClassifyType,
  MaterialType
} from '@/prisma/generated';

export class BaseContractMaterialDetailDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '组织id（项目id）' })
  orgId: string;

  @ApiProperty({ description: '租户id' })
  tenantId: string;

  @ApiProperty({ description: '物资合同id' })
  @IsNotEmpty({ message: '物资合同id不能为空' })
  @IsString({ message: '物资合同id必须是字符串' })
  materialContractId: string;

  @ApiProperty({ description: '材料字典版本id' })
  @IsNotEmpty({ message: '材料字典版本id不能为空' })
  @IsString({ message: '材料字典版本id必须是字符串' })
  materialDictionaryVersionId: string;

  @ApiProperty({ description: '材料字典分类id' })
  @IsNotEmpty({ message: '材料字典分类id不能为空' })
  @IsString({ message: '材料字典分类id必须是字符串' })
  materialDictionaryCategoryId: string;

  @ApiProperty({ description: '材料字典明细id' })
  @IsNotEmpty({ message: '材料字典明细id不能为空' })
  @IsString({ message: '材料字典明细id必须是字符串' })
  materialDictionaryDetailId: string;

  @ApiProperty({ description: '源合同/补充协议id' })
  @IsOptional({ message: '源合同/补充协议id可以为空' })
  @IsString({ message: '源合同/补充协议id必须是字符串' })
  sourceMaterialContractId?: string;

  @ApiProperty({ description: '单位' })
  @IsOptional({ message: '单位可以为空' })
  @IsString({ message: '单位必须是字符串' })
  unit?: string;

  // 变更前
  @ApiProperty({ description: '原不含税单价' })
  @IsOptional({ message: '原不含税单价可以为空' })
  @IsNumber({}, { message: '原不含税单价必须是数字' })
  priceExcludingTax?: number;

  @ApiProperty({ description: '原增值税额' })
  @IsOptional({ message: '原增值税额可以为空' })
  @IsNumber({}, { message: '原增值税额必须是数字' })
  addedTaxAmount?: number;

  @ApiProperty({ description: '原含税单价' })
  @IsOptional({ message: '原含税单价可以为空' })
  @IsNumber({}, { message: '原含税单价必须是数字' })
  priceIncludingTax?: number;

  @ApiProperty({ description: '原暂定数量' })
  @IsOptional({ message: '原暂定数量可以为空' })
  @IsNumber({}, { message: '原暂定数量必须是数字' })
  provisionalQuantity?: number;

  // 变更后
  @ApiProperty({ description: '变更数量' })
  @IsNotEmpty({ message: '变更数量不可以为空' })
  @IsNumber({}, { message: '变更数量必须是数字' })
  changeQuantity?: number;

  @ApiProperty({ description: '变更后不含税单价' })
  @IsNotEmpty({ message: '变更后不含税单价不可以为空' })
  @IsNumber({}, { message: '变更后不含税单价必须是数字' })
  changePriceExcludingTax?: number;

  @ApiProperty({ description: '变更后含税单价' })
  @IsNotEmpty({ message: '变更后含税单价不可以为空' })
  @IsNumber({}, { message: '变更后含税单价必须是数字' })
  changePriceIncludingTax?: number;

  @ApiProperty({ description: '变更后增值税额' })
  @IsNotEmpty({ message: '变更后增值税额不可以为空' })
  @IsNumber({}, { message: '变更后增值税额必须是数字' })
  changeAddedTaxAmount?: number;

  @ApiProperty({ description: '变更类型' })
  @IsOptional({ message: '变更类型可以为空' })
  @IsIn(Object.values(AlterType), {
    message: '变更类型不在枚举范围内'
  })
  alterType?: AlterType;

  @ApiProperty({ description: '排序' })
  sort: string;

  @ApiProperty({ description: '合同说明' })
  @IsOptional({ message: '合同说明可以为空' })
  @IsString({ message: '合同说明必须是字符串' })
  remark?: string;

  // 变更详情
  @ApiProperty({ description: '变更价格增减' })
  @IsOptional({ message: '变更价格增减可以为空' })
  @IsNumber({}, { message: '变更价格增减必须是数字' })
  priceCalculate?: number;

  @ApiProperty({ description: '变更数量增减' })
  @IsOptional({ message: '变更数量增减可以为空' })
  @IsNumber({}, { message: '变更数量增减必须是数字' })
  quantityCalculate?: number;

  // 租赁周转不需要的
  @ApiProperty({ description: '变更前不含税总价' })
  @IsOptional({ message: '变更前不含税总价可以为空' })
  @IsNumber({}, { message: '变更前不含税总价必须是数字' })
  totalPriceExcludingTax?: number;

  @ApiProperty({ description: '结算数量' })
  @IsOptional({ message: '结算数量可以为空' })
  @IsNumber({}, { message: '结算数量必须是数字' })
  settlementQuantity?: number;

  @ApiProperty({ description: '变更前增值税总额' })
  @IsOptional({ message: '变更前增值税总额可以为空' })
  @IsNumber({}, { message: '变更前增值税总额必须是数字' })
  totalValueAddedTaxAmount?: number;

  @ApiProperty({ description: '变更前含税总价' })
  @IsOptional({ message: '变更前含税总价可以为空' })
  @IsNumber({}, { message: '变更前含税总价必须是数字' })
  totalPriceIncludingTax?: number;

  @ApiProperty({ description: '变更后不含税总价' })
  @IsNotEmpty({ message: '变更后不含税总价不可以为空' })
  @IsNumber({}, { message: '变更后不含税总价必须是数字' })
  changeTotalPriceExcludingTax?: number;

  @ApiProperty({ description: '变更后增值税总额' })
  @IsNotEmpty({ message: '变更后增值税总额不可以为空' })
  @IsNumber({}, { message: '变更后增值税总额必须是数字' })
  changeTotalValueAddedTaxAmount?: number;

  @ApiProperty({ description: '变更后含税总价' })
  @IsNotEmpty({ message: '变更后含税总价不可以为空' })
  @IsNumber({}, { message: '变更后含税总价必须是数字' })
  changeTotalPriceIncludingTax?: number;

  @ApiProperty({ description: '增减百分比' })
  @IsOptional({ message: '增减百分比可以为空' })
  @IsString({ message: '增减百分比必须是字符串' })
  changePercentage?: string;

  // 变更增减金额
  @ApiProperty({ description: '变更增减不含税总价' })
  @IsOptional({ message: '变更增减不含税总价可以为空' })
  @IsNumber({}, { message: '变更增减不含税总价必须是数字' })
  changeCalculateTotalPriceExcludingTax?: number;

  @ApiProperty({ description: '变更增减增值税总额' })
  @IsOptional({ message: '变更增减增值税总额可以为空' })
  @IsNumber({}, { message: '变更增减增值税总额必须是数字' })
  changeCalculateTotalValueAddedTaxAmount?: number;

  @ApiProperty({ description: '变更增减含税总价' })
  @IsOptional({ message: '变更增减含税总价可以为空' })
  @IsNumber({}, { message: '变更增减含税总价必须是数字' })
  changeCalculateTotalPriceIncludingTax?: number;

  // 租赁周转
  @ApiProperty({ description: '原暂定天数' })
  @IsOptional({ message: '原暂定天数可以为空' })
  @IsNumber({}, { message: '原暂定天数必须是数字' })
  provisionalDays?: number;

  @ApiProperty({ description: '变更后天数' })
  @IsNotEmpty({ message: '变更后天数不可以为空' })
  @IsNumber({}, { message: '变更后天数必须是数字' })
  changeProvisionalDays: number;

  // 物资合同
  @ApiProperty({ description: '材质、性能参数等（或执行的技术质量标准' })
  @IsOptional({ message: '材质、性能参数等（或执行的技术质量标准可以为空' })
  @IsString({ message: '源合同/补充协议id必须是字符串' })
  qualityStandard?: string;

  @ApiProperty({ description: '源合同/品牌或厂家' })
  @IsOptional({ message: '品牌或厂家可以为空' })
  @IsString({ message: '品牌或厂家必须是字符串' })
  brand?: string;

  // 材料信息
  @ApiProperty({ description: '材料名称' })
  name: string;

  @ApiProperty({ description: '材料编码' })
  code: string;

  @ApiProperty({ description: '材料规格型号' })
  specificationModel: string;

  @ApiProperty({ description: '材料类型' })
  type: MaterialType;

  @ApiProperty({ description: '合同范本类型' })
  @IsNotEmpty({ message: 'contractTemplateType不能为空' })
  @IsIn(Object.values(ContractTemplateClassifyType), {
    message: '合同范本类型不在枚举范围内'
  })
  @IsString({ message: 'contractTemplateType类型错误' })
  contractTemplateType: ContractTemplateClassifyType;
}

// 新增合同明细
export class ContractDetailsListCreateDto {
  @ApiProperty({ description: '明细数组' })
  @IsNotEmpty({ message: '明细数组不能为空' })
  @IsArray({ message: '明细数组格式不正确' })
  list: ContractDetailCreateDto[];

  @ApiProperty({ description: '合同ID' })
  @IsNotEmpty({ message: '合同ID不能为空' })
  @IsString({ message: '合同ID格式不正确' })
  materialContractId: string;

  @ApiProperty({ description: '合同类型' })
  @IsNotEmpty({ message: '合同类型不能为空' })
  @IsString({ message: '合同类型格式不正确' })
  contractTemplateType: ContractTemplateClassifyType;
}

export class ContractDetailCreateDto extends PickType(
  BaseContractMaterialDetailDto,
  [
    'materialDictionaryVersionId',
    'materialDictionaryCategoryId',
    'materialDictionaryDetailId',
    'unit'
  ]
) {}

// 编辑合同明细
export class ContractDetailUpdateDto extends PickType(
  BaseContractMaterialDetailDto,
  [
    'contractTemplateType',
    'unit',
    'changeQuantity',
    'changePriceExcludingTax',
    'changePriceIncludingTax',
    'changeAddedTaxAmount',
    'alterType',
    'remark',
    'changePercentage',
    'priceCalculate',
    'quantityCalculate',
    'settlementQuantity',
    'totalValueAddedTaxAmount',
    'changeTotalPriceExcludingTax',
    'changeTotalValueAddedTaxAmount',
    'changeTotalPriceIncludingTax',
    'changeCalculateTotalPriceExcludingTax',
    'changeCalculateTotalValueAddedTaxAmount',
    'changeCalculateTotalPriceIncludingTax',
    'qualityStandard',
    'brand',
    'changeProvisionalDays'
  ]
) {}

export class QueryChooseMaterialDetailDto extends PickType(
  BaseContractMaterialDetailDto,
  ['contractTemplateType']
) {}

export class ContractConsumeMaterialDetailsDataDto extends PickType(
  BaseContractMaterialDetailDto,
  [
    'id',
    'materialContractId',
    'materialDictionaryVersionId',
    'materialDictionaryCategoryId',
    'materialDictionaryDetailId',
    'sourceMaterialContractId',
    'unit',
    'priceExcludingTax',
    'addedTaxAmount',
    'priceIncludingTax',
    'provisionalQuantity',
    'changeQuantity',
    'changePriceExcludingTax',
    'changePriceIncludingTax',
    'changeAddedTaxAmount',
    'alterType',
    'remark',
    'priceCalculate',
    'quantityCalculate',
    'totalPriceExcludingTax',
    'settlementQuantity',
    'totalValueAddedTaxAmount',
    'totalPriceIncludingTax',
    'changeTotalPriceExcludingTax',
    'changeTotalValueAddedTaxAmount',
    'changeTotalPriceIncludingTax',
    'changePercentage',
    'changeCalculateTotalPriceExcludingTax',
    'changeCalculateTotalValueAddedTaxAmount',
    'changeCalculateTotalPriceIncludingTax',
    'qualityStandard',
    'brand',
    'name',
    'code',
    'specificationModel',
    'type'
  ]
) {}

export class ContractConsumeMaterialDetailsResDto {
  @ApiProperty({
    description: '税率',
    type: String
  })
  taxRate: string;

  @ApiProperty({
    description: '合同明细数据',
    type: ContractConsumeMaterialDetailsDataDto,
    isArray: true
  })
  detailList: ContractConsumeMaterialDetailsDataDto[];
}

export class ContractConcreteDetailsResDto extends PickType(
  BaseContractMaterialDetailDto,
  [
    'id',
    'materialContractId',
    'materialDictionaryVersionId',
    'materialDictionaryCategoryId',
    'materialDictionaryDetailId',
    'sourceMaterialContractId',
    'unit',
    'priceExcludingTax',
    'addedTaxAmount',
    'priceIncludingTax',
    'provisionalQuantity',
    'changeQuantity',
    'changePriceExcludingTax',
    'changePriceIncludingTax',
    'changeAddedTaxAmount',
    'alterType',
    'remark',
    'priceCalculate',
    'quantityCalculate',
    'totalPriceExcludingTax',
    'settlementQuantity',
    'totalValueAddedTaxAmount',
    'totalPriceIncludingTax',
    'changeTotalPriceExcludingTax',
    'changeTotalValueAddedTaxAmount',
    'changeTotalPriceIncludingTax',
    'changePercentage',
    'changeCalculateTotalPriceExcludingTax',
    'changeCalculateTotalValueAddedTaxAmount',
    'changeCalculateTotalPriceIncludingTax',
    'qualityStandard',
    'brand',
    'name',
    'code'
  ]
) {}

export class ContractTurnoverMaterialDetailsResDto extends PickType(
  BaseContractMaterialDetailDto,
  [
    'id',
    'materialContractId',
    'materialDictionaryVersionId',
    'materialDictionaryCategoryId',
    'materialDictionaryDetailId',
    'sourceMaterialContractId',
    'unit',
    'priceExcludingTax',
    'addedTaxAmount',
    'priceIncludingTax',
    'provisionalQuantity',
    'changeQuantity',
    'changePriceExcludingTax',
    'changePriceIncludingTax',
    'changeAddedTaxAmount',
    'alterType',
    'remark',
    'priceCalculate',
    'quantityCalculate',
    'provisionalDays',
    'changeProvisionalDays',
    'qualityStandard',
    'brand',
    'name',
    'code'
  ]
) {}

export class ChooseMaterialCategoryResDto {
  @ApiProperty({
    description: '材料版本id'
  })
  materialDictionaryVersionId: string;

  @ApiProperty({
    description: '材料分类id'
  })
  id: string;

  @ApiProperty({
    description: '编码'
  })
  code: string;

  @ApiProperty({
    description: '名称'
  })
  name: string;

  @ApiProperty({
    description: '材料类型'
  })
  type: string;

  @ApiProperty({
    description: '备注'
  })
  remark: string;
}

export class ChooseMaterialDetailResDto {
  @ApiProperty({
    description: '材料版本id'
  })
  materialDictionaryVersionId: string;

  @ApiProperty({
    description: '材料分类id'
  })
  materialDictionaryCategoryId: string;

  @ApiProperty({
    description: '材料明细id'
  })
  id: string;

  @ApiProperty({
    description: '编码'
  })
  code: string;

  @ApiProperty({
    description: '名称'
  })
  name: string;

  @ApiProperty({
    description: '规格型号'
  })
  specificationModel: string;

  @ApiProperty({
    description: '计量单位'
  })
  meteringUnit: string;
}

export class QueryMaterialDetailDto {
  @ApiProperty({
    description: '合同id'
  })
  @IsOptional()
  @IsString({ message: '合同id必须为字符串' })
  materialContractId: string;

  @ApiProperty({
    description: '分类id'
  })
  @IsOptional()
  @IsString({ message: '分类id必须为字符串' })
  materialDictionaryCategoryId?: string;

  @ApiProperty({
    description: '名称'
  })
  @IsOptional()
  @IsString({ message: '名称必须为字符串' })
  name?: string;
}

export class QueryChooseMateriaCategoryDto {
  @ApiProperty({
    description: '名称'
  })
  @IsOptional()
  @IsString({ message: '名称必须为字符串' })
  name?: string;
}

export class EditMaterialDetailMoveDto extends EditMoveDto {
  @ApiProperty({
    description: '合同id'
  })
  @IsNotEmpty({ message: '合同id不能为空' })
  @IsString({ message: '合同id类型错误' })
  materialContractId: string;

  @ApiProperty({ description: '合同范本类型' })
  @IsNotEmpty({ message: 'contractTemplateType不能为空' })
  @IsIn(Object.values(ContractTemplateClassifyType), {
    message: '合同范本类型不在枚举范围内'
  })
  @IsString({ message: 'contractTemplateType类型错误' })
  contractTemplateType: ContractTemplateClassifyType;
}
