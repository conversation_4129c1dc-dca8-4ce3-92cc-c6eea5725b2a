import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class BaseContractConcreteSurchargeDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '合同id' })
  materialContractId: string;

  @ApiProperty({ description: '名称' })
  name: string;

  @ApiProperty({ description: '单位' })
  unit: string;

  @ApiProperty({ description: '变更前价格' })
  price: number;

  @ApiProperty({ description: '变更后价格' })
  @IsNotEmpty({ message: '变更后价格不能为空' })
  @IsNumber({}, { message: '变更后价格必须是数字' })
  changePrice: number;

  @ApiProperty({ description: '变更计算价格' })
  @IsOptional({ message: '变更计算价格可以为空' })
  @IsNumber({}, { message: '文件名称必须是数字' })
  changeCalculatePrice?: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string;
}

export class ContractConcreteSurchargeResDto extends PickType(
  BaseContractConcreteSurchargeDto,
  [
    'id',
    'name',
    'price',
    'changePrice',
    'unit',
    'changeCalculatePrice',
    'materialContractId',
    'remark'
  ] as const
) {}

export class ContractConcreteSurchargeUpdateDto extends PickType(
  BaseContractConcreteSurchargeDto,
  ['changePrice', 'changeCalculatePrice', 'remark'] as const
) {}
