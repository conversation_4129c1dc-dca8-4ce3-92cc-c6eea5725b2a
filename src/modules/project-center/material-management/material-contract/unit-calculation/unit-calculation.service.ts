import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  UnitCalculationCreateDto,
  UnitCalculationQueryDto,
  UnitCalculationUpdateDto
} from './unit-calculation.dto';

@Injectable()
export class UnitCalculationService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(query: UnitCalculationQueryDto, reqUser: IReqUser) {
    const { materialDetailId, materialContractId } = query;
    const { tenantId, orgId } = reqUser;
    const materialDictionaryDetail =
      await this.prisma.materialDictionaryDetail.findUnique({
        where: {
          id: materialDetailId,
          tenantId: tenantId,
          isDeleted: false
        }
      });
    const res = await this.prisma.$queryRaw<any[]>`
      select id, unit, factor, remark, false as is_operation, null as material_detail_id from material_dictionary_unit_calculation mduc
      where material_dictionary_detail_id = ${materialDetailId}
      and is_deleted = false

      UNION all

      select id, unit, factor, remark, true as is_operation, material_detail_id from material_contract_unit_calculation mduc
      where material_detail_id = ${materialDetailId}
      and material_contract_id = ${materialContractId}
      and is_deleted = false
      and tenant_id = ${tenantId}
      and org_id = ${orgId}
    `;
    return {
      dictionaryUnit: materialDictionaryDetail?.meteringUnit ?? '',
      list: res
    };
  }

  async add(reqUser: IReqUser, data: UnitCalculationCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;
    return await this.prisma.materialContractUnitCalculation.create({
      data: {
        ...data,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async update(id: string, reqUser: IReqUser, data: UnitCalculationUpdateDto) {
    const { tenantId, orgId, id: userId } = reqUser;
    return await this.prisma.materialContractUnitCalculation.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: userId
      }
    });
  }

  async delete(id: string, reqUser: IReqUser) {
    return await this.prisma.materialContractUnitCalculation.update({
      where: {
        id
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
