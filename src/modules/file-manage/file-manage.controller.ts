import {
  BadRequestException,
  Controller,
  Get,
  Param,
  Post,
  Req,
  Res,
  UseInterceptors
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FastifyReply } from 'fastify';
import { FastifyRequest } from 'fastify/types/request';
import { Utils } from 'src/public/utils';
import * as uuid from 'uuid';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import { UploadRequestBody } from './dto/file.dto';
import { FileManageService } from './file-manage.service';
import { FastifyFileInterceptor } from './interceptor/fastify-file.interceptor';

@ApiTags('文件管理')
@Controller('file-manage')
export class FileManageController {
  constructor(private readonly service: FileManageService) {}

  @ApiOperation({
    summary: '文件上传',
    description: '文件上传'
  })
  @ApiResponse({ status: 200, description: '文件上传成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/upload/:fileName')
  @UseInterceptors(new FastifyFileInterceptor('file'))
  async upload(
    @Req() request: FastifyRequest,
    @Param('fileName') fileName: string,
    @ReqUser() reqUser: IReqUser
  ) {
    const body = request.body as UploadRequestBody;

    if (!body.file) {
      throw new BadRequestException('未上传文件');
    }

    console.log(
      '',
      body.file.fieldname,
      body.file.mimetype,
      body.file.originalname,
      body.file.buffer.length
    );

    const newFileName =
      !fileName || fileName === ''
        ? uuid.v7() + '.' + body.file.originalname.split('.')[1]
        : fileName;

    // 保存至obs
    const url = await this.service.upload(
      newFileName,
      body.file.buffer,
      body.file.mimetype,
      reqUser
    );

    return {
      url,
      fileName: newFileName,
      size: Utils.formatFileSize(body.file.buffer.length),
      suffix: body.file.originalname.split('.')[1]
    };
  }

  @ApiOperation({
    summary: '文件下载',
    description: '文件下载'
  })
  @ApiResponse({ status: 200, description: '文件下载成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/down/:fileName')
  async down(
    @Param('fileName') fileName: string,
    @Res() res: FastifyReply,
    @ReqUser() reqUser: IReqUser
  ) {
    // 文件下载
    const bis = await this.service.download(fileName, reqUser);
    res
      .header(
        'Content-Disposition',
        `attachment; filename=output.${fileName.split('.')[1]}`
      )
      .header('Content-Type', 'application/octet-stream')
      .send(bis); // 直接发送 流 数据
  }
}
