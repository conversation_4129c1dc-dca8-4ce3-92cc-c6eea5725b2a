[{"name": "项目名称", "classify": "PROJECT_INFORMATION", "isRequired": true, "code": "项目名称", "isDefaultRequired": true, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "ORG", "sort": 1, "isMatching": false}, {"name": "工程名称", "classify": "PROJECT_INFORMATION", "isRequired": false, "code": "工程名称", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "ORG", "sort": 2, "isMatching": false}, {"name": "工程地址", "classify": "PROJECT_INFORMATION", "isRequired": false, "code": "工程地址", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_PROJECT_INFORMATION", "sort": 3, "isMatching": true}, {"name": "工程概况", "classify": "PROJECT_INFORMATION", "isRequired": false, "code": "工程概况", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_PROJECT_INFORMATION", "sort": 4, "isMatching": false}, {"name": "建设单位", "classify": "PROJECT_INFORMATION", "isRequired": false, "code": "建设单位", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_PROJECT_INFORMATION", "sort": 5, "isMatching": false}, {"name": "甲方名称", "classify": "Party_A_INFORMATION", "isRequired": true, "code": "甲方名称", "isDefaultRequired": true, "isUpdate": true, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 6, "isMatching": true}, {"name": "统一社会信用代码（甲方）", "classify": "Party_A_INFORMATION", "isRequired": false, "code": "统一社会信用代码（甲方）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 7, "isMatching": false}, {"name": "注册地址（甲方）", "classify": "Party_A_INFORMATION", "isRequired": false, "code": "注册地址（甲方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 8, "isMatching": false}, {"name": "开户行账号（甲方）", "classify": "Party_A_INFORMATION", "isRequired": false, "code": "开户行账号（甲方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 9, "isMatching": false}, {"name": "开户银行（甲方）", "classify": "Party_A_INFORMATION", "isRequired": false, "code": "开户银行（甲方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 10, "isMatching": false}, {"name": "联系电话（甲方）", "classify": "Party_A_INFORMATION", "isRequired": false, "code": "联系电话（甲方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 11, "isMatching": false}, {"name": "项目经理", "classify": "Party_A_INFORMATION", "isRequired": false, "code": "项目经理", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 12, "isMatching": false}, {"name": "项目经理电话", "classify": "Party_A_INFORMATION", "isRequired": false, "code": "项目经理电话", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "BASIC_COMPANY_INFORMATION", "sort": 13, "isMatching": false}, {"name": "乙方名称", "classify": "Party_B_INFORMATION", "isRequired": true, "code": "乙方名称", "isDefaultRequired": true, "isUpdate": true, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 14, "isMatching": true}, {"name": "统一社会信用代码（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "统一社会信用代码（乙方）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 15, "isMatching": false}, {"name": "统一社会信用代码有效期（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "统一社会信用代码有效期（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 16, "isMatching": false}, {"name": "资质证书编号（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "资质证书编号（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 17, "isMatching": false}, {"name": "资质证书有效期（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "资质证书有效期（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 18, "isMatching": false}, {"name": "安全生产许可证号码（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "安全生产许可证号码（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 19, "isMatching": false}, {"name": "安全生产许可证有效期（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "安全生产许可证有效期（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 20, "isMatching": false}, {"name": "增值税纳税人资格（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "增值税纳税人资格（乙方）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "ENUM", "enumValue": "一般纳税人,小规模纳税人", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 21, "isMatching": false}, {"name": "收款银行账号（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "收款银行账号（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 22, "isMatching": false}, {"name": "开户银行（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "开户银行（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 23, "isMatching": false}, {"name": "乙方地址", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "乙方地址", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 24, "isMatching": false}, {"name": "邮编（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "邮编（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 25, "isMatching": false}, {"name": "联系人（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "联系人（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 26, "isMatching": false}, {"name": "联系人电话（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "联系人电话（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 27, "isMatching": false}, {"name": "乙方传真号码", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "乙方传真号码", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 28, "isMatching": false}, {"name": "乙方电子邮箱", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "乙方电子邮箱", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 29, "isMatching": false}, {"name": "营业执照号码（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "营业执照号码（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 30, "isMatching": false}, {"name": "营业执照有效期（乙方）", "classify": "Party_B_INFORMATION", "isRequired": false, "code": "营业执照有效期（乙方）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "PROVIDER_DIRECTORY", "sort": 31, "isMatching": false}, {"name": "合同编号", "classify": "CONTRACT_INFORMATION", "isRequired": true, "code": "合同编号", "isDefaultRequired": true, "isUpdate": true, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "CODE_RULE", "sort": 32, "isMatching": true}, {"name": "签约日期", "classify": "CONTRACT_INFORMATION", "isRequired": true, "code": "签约日期", "isDefaultRequired": true, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 33, "isMatching": true}, {"name": "签约地点", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "签约地点", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 34, "isMatching": false}, {"name": "合同计划开始时间", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同计划开始时间", "isDefaultRequired": false, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 35, "isMatching": false}, {"name": "合同计划完工时间", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同计划完工时间", "isDefaultRequired": false, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 36, "isMatching": false}, {"name": "计划总工期（日历天）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "计划总工期（日历天）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "NUMBER", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 37, "isMatching": false}, {"name": "合同含税总金额（小写，元）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同含税总金额（小写，元）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "NUMBER", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 38, "isMatching": false}, {"name": "合同含税总金额（大写，元）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同含税总金额（大写，元）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 39, "isMatching": false}, {"name": "合同不含税总金额（小写，元）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同不含税总金额（小写，元）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "NUMBER", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 40, "isMatching": true}, {"name": "合同不含税总金额（大写，元）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同不含税总金额（大写，元）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 41, "isMatching": true}, {"name": "增值税税率", "classify": "CONTRACT_INFORMATION", "isRequired": true, "code": "增值税税率", "isDefaultRequired": true, "isUpdate": false, "fieldType": "PERCENT", "enumValue": "", "type": "GENERAL", "moduleSource": "TAX_RATE_DICTIONARY", "sort": 42, "isMatching": true}, {"name": "合同增值税金额（小写，元）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同增值税金额（小写，元）", "isDefaultRequired": false, "isUpdate": true, "fieldType": "PERCENT", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 43, "isMatching": false}, {"name": "分项工程全费用综合单价表1", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "分项工程全费用综合单价表1", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "SUBPACKAGE_LABOUR_SERVICE", "moduleSource": "SYSTEM_BUILT", "sort": 45, "isMatching": false}, {"name": "分项工程全费用综合单价表1", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "分项工程全费用综合单价表1", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "SUBPACKAGE_LABOUR_SPECIALTY", "moduleSource": "SYSTEM_BUILT", "sort": 46, "isMatching": false}, {"name": "分项工程固定综合单价表", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "分项工程固定综合单价表", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "SUBPACKAGE_LABOUR_SERVICE", "moduleSource": "SYSTEM_BUILT", "sort": 47, "isMatching": false}, {"name": "分项工程固定综合单价表", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "分项工程固定综合单价表", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "SUBPACKAGE_LABOUR_SPECIALTY", "moduleSource": "SYSTEM_BUILT", "sort": 48, "isMatching": false}, {"name": "分项工程全费用综合单价表2", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "分项工程全费用综合单价表2", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "SUBPACKAGE_LABOUR_SERVICE", "moduleSource": "SYSTEM_BUILT", "sort": 49, "isMatching": false}, {"name": "分项工程全费用综合单价表2", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "分项工程全费用综合单价表2", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "SUBPACKAGE_LABOUR_SPECIALTY", "moduleSource": "SYSTEM_BUILT", "sort": 50, "isMatching": false}, {"name": "货物清单表（采购）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "货物清单表（采购）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "MATERIALS_PURCHASING", "moduleSource": "SYSTEM_BUILT", "sort": 51, "isMatching": false}, {"name": "货物清单表（商混）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "货物清单表（商混）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "MATERIALS_COMMERCIAL_CONCRETE", "moduleSource": "SYSTEM_BUILT", "sort": 52, "isMatching": false}, {"name": "货物清单表（租赁）", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "货物清单表（租赁）", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "MATERIALS_LEASING_TURNOVER", "moduleSource": "SYSTEM_BUILT", "sort": 53, "isMatching": false}, {"name": "费率表", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "费率表", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "OTHERS", "moduleSource": "SYSTEM_BUILT", "sort": 54, "isMatching": false}, {"name": "租赁机械表", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "租赁机械表", "isDefaultRequired": false, "isUpdate": false, "fieldType": "TABLE", "enumValue": "", "type": "MACHINERY", "moduleSource": "SYSTEM_BUILT", "sort": 55, "isMatching": false}, {"name": "付款时间", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "付款时间", "isDefaultRequired": false, "isUpdate": false, "fieldType": "DATE", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 56, "isMatching": false}, {"name": "付款比例", "classify": "CONTRACT_INFORMATION", "isRequired": true, "code": "付款比例", "isDefaultRequired": true, "isUpdate": false, "fieldType": "TEXT", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 57, "isMatching": false}, {"name": "单价类型", "classify": "CONTRACT_INFORMATION", "isRequired": true, "code": "单价类型", "isDefaultRequired": true, "isUpdate": false, "fieldType": "ENUM", "enumValue": "固定单价,可调单价", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 58, "isMatching": false}, {"name": "合同履约保证金", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "合同履约保证金", "isDefaultRequired": false, "isUpdate": false, "fieldType": "NUMBER", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 59, "isMatching": false}, {"name": "质保金比例", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "质保金比例", "isDefaultRequired": false, "isUpdate": false, "fieldType": "PERCENT", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 60, "isMatching": false}, {"name": "预付款", "classify": "CONTRACT_INFORMATION", "isRequired": false, "code": "预付款", "isDefaultRequired": false, "isUpdate": false, "fieldType": "NUMBER", "enumValue": "", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 61, "isMatching": false}, {"name": "扣除方式", "classify": "CONTRACT_INFORMATION", "isRequired": true, "code": "扣除方式", "isDefaultRequired": true, "isUpdate": false, "fieldType": "ENUM", "enumValue": "按批次扣除,按结算周期扣除", "type": "MATERIALS_COMMERCIAL_CONCRETE", "moduleSource": "SYSTEM_BUILT", "sort": 62, "isMatching": false}, {"name": "发票类型", "classify": "CONTRACT_INFORMATION", "isRequired": true, "code": "发票类型", "isDefaultRequired": true, "isUpdate": false, "fieldType": "ENUM", "enumValue": "增值税专用发票,增值税普通发票", "type": "GENERAL", "moduleSource": "SYSTEM_BUILT", "sort": 63, "isMatching": false}]