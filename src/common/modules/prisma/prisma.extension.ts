import * as dayjs from 'dayjs';

import { Prisma } from '@/prisma/generated';

// #region utility functions
function setDefaultDateTime(data: any, fields: ('createAt' | 'updateAt')[]) {
  if (!data) {
    return;
  }

  // 强制改为 +8 小时，因为 prisma 默认存储的是 UTC 时间
  const now = dayjs().add(8, 'hour').toISOString();
  const fillValue = (d: any) => {
    for (const field of fields) {
      if (d[field] == null) {
        d[field] = now;
      }
    }
  };

  if (Array.isArray(data)) {
    for (const d of data) {
      fillValue(d);
    }
  } else if (typeof data === 'object') {
    fillValue(data);
  }
}

function formatDateTime(dateTime: Date) {
  if (!dateTime) {
    return;
  }
  return dayjs(dateTime).subtract(8, 'hour').format('YYYY-MM-DD HH:mm:ss');
}

function snakeCaseToCamelCase(data: any) {
  if (Array.isArray(data)) {
    for (const item of data) {
      snakeCaseToCamelCase(item);
    }
  }
  if (data && typeof data === 'object') {
    for (const key in data) {
      if (key.includes('_')) {
        const newKey = key.replace(/_([a-z])/g, (match, letter) =>
          letter.toUpperCase()
        );
        data[newKey] = data[key];
        delete data[key];
      }
    }
    // 格式化创建时间、更新时间
    if (data['createAt'] != null) {
      data['createAt'] = formatDateTime(new Date(data['createAt']));
    }
    if (data['updateAt'] != null) {
      data['updateAt'] = formatDateTime(new Date(data['updateAt']));
    }
  }
}

function decimalToNumber(data: any) {
  if (Array.isArray(data)) {
    for (const item of data) {
      decimalToNumber(item);
    }
  }
  if (data && typeof data === 'object') {
    for (const key in data) {
      if (data[key] instanceof Prisma.Decimal) {
        data[key] = data[key].toNumber();
      }
    }
  }
}
// #endregion

/** 模型查询时排除已删除的数据 */
const excludeDeletedDataExt = Prisma.defineExtension({
  query: {
    $allModels: {
      async findFirst({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      },
      async findFirstOrThrow({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      },
      async findUnique({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      },
      async findUniqueOrThrow({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      },
      async findMany({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      },
      async count({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      },
      async aggregate({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      },
      async groupBy({ args, query }) {
        if (
          args.where &&
          'isDeleted' in args.where &&
          args.where.isDeleted == null
        ) {
          args.where = {
            ...args.where,
            isDeleted: false
          };
        }
        return await query(args);
      }
    }
  }
});

/** 设置模型操作数据的创建时间、更新时间的默认时间 */
const setDefaultDateTimeExt = Prisma.defineExtension({
  query: {
    $allModels: {
      // { model, operation, args, query }
      async create({ args, query }) {
        setDefaultDateTime(args.data, ['createAt', 'updateAt']);
        return await query(args);
      },
      async createMany({ args, query }) {
        setDefaultDateTime(args.data, ['createAt', 'updateAt']);
        return await query(args);
      },
      async update({ args, query }) {
        setDefaultDateTime(args.data, ['updateAt']);
        return await query(args);
      },
      async updateMany({ args, query }) {
        setDefaultDateTime(args.data, ['updateAt']);
        return await query(args);
      },
      async upsert({ args, query }) {
        setDefaultDateTime(args.create, ['createAt', 'updateAt']);
        setDefaultDateTime(args.update, ['updateAt']);
        return await query(args);
      }
    }
  }
});

/** 格式化查询结果的创建时间、更新时间字段值 */
const formatResultDateTimeExt = Prisma.defineExtension({
  result: {
    $allModels: {
      createAt: {
        compute(data: any) {
          if (data.createAt) {
            return formatDateTime(data.createAt);
          } else {
            return data.createAt;
          }
        }
      },
      updateAt: {
        compute(data: any) {
          if (data.updateAt) {
            return formatDateTime(data.updateAt);
          } else {
            return data.updateAt;
          }
        }
      }
    }
  }
});

/** 将sql查询结果中的字段名转换为驼峰命名 */
const formatResultFieldExt = Prisma.defineExtension({
  query: {
    async $allOperations({ operation, args, query }) {
      const result = await query(args);

      if (operation === '$queryRaw') {
        snakeCaseToCamelCase(result);
      }

      return result;
    }
  }
});

/** 将所有查询结果的 Decimal 类型字段值转换为 Number */
const transformDecimalToNumberExt = Prisma.defineExtension({
  query: {
    async $allOperations({ operation, args, query }) {
      const result = await query(args);

      decimalToNumber(result);

      if (result) return result;
    }
  }
});

export const PrismaExtensions = {
  excludeDeletedDataExt,
  setDefaultDateTimeExt,
  formatResultDateTimeExt,
  formatResultFieldExt,
  transformDecimalToNumberExt
};
